#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel后处理脚本
对最终合并文件进行格式化处理
"""

import pandas as pd
import re
from pathlib import Path
from openpyxl import load_workbook
from openpyxl.styles import Font, PatternFill
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ExcelPostProcessor:
    def __init__(self):
        """初始化Excel后处理器"""
        self.yellow_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")
        self.bold_font = Font(bold=True)
        
    def process_excel_file(self, input_file, output_file=None):
        """处理Excel文件"""
        try:
            input_path = Path(input_file)
            if not input_path.exists():
                logger.error(f"输入文件不存在: {input_file}")
                return False
            
            if output_file is None:
                output_file = input_path.parent / f"{input_path.stem}_processed{input_path.suffix}"
            
            logger.info(f"开始处理Excel文件: {input_file}")
            
            # 读取Excel文件
            df = pd.read_excel(input_file)
            logger.info(f"原始数据: {len(df)} 行 x {len(df.columns)} 列")
            
            # 1. 删除含有"unnamed"的单元格
            df = self._remove_unnamed_cells(df)
            
            # 2. 检测每行只有一个单元格有数据的，挪到最左侧
            df = self._move_single_data_to_left(df)
            
            # 3. 处理D列和C列的特殊格式
            df = self._process_time_amount_format(df)
            
            # 4. 处理A列的"汉字+6位数字"格式，提取数字到新列
            df = self._extract_stock_codes(df)
            
            # 保存处理后的数据到Excel
            df.to_excel(output_file, index=False)
            
            # 5. 处理单元格格式（标黄加粗含*的单元格）
            self._format_asterisk_cells(output_file)
            
            logger.info(f"处理完成: {output_file}")
            logger.info(f"最终数据: {len(df)} 行 x {len(df.columns)} 列")
            
            return True
            
        except Exception as e:
            logger.error(f"处理Excel文件失败: {e}")
            return False

    def _remove_unnamed_cells(self, df):
        """删除含有unnamed的单元格"""
        logger.info("删除含有unnamed的单元格...")

        removed_count = 0

        # 遍历所有单元格，将含有"unnamed"的单元格清空
        for col_idx in range(len(df.columns)):
            for row_idx in range(len(df)):
                cell_value = df.iloc[row_idx, col_idx]
                if pd.notna(cell_value) and 'unnamed' in str(cell_value).lower():
                    df.iloc[row_idx, col_idx] = ''
                    removed_count += 1
                    # logger.debug(f"清空单元格 ({row_idx+1}, {col_idx+1}): '{cell_value}'")

        logger.info(f"清空了 {removed_count} 个含unnamed的单元格")
        return df
    
    def _move_single_data_to_left(self, df):
        """将每行只有一个单元格有数据的挪到最左侧"""
        logger.info("处理单数据行，移动到最左侧...")
        
        moved_count = 0
        for index, row in df.iterrows():
            # 检查该行有多少个非空单元格
            non_empty_cells = []
            for col_idx, value in enumerate(row):
                if pd.notna(value) and str(value).strip() != '':
                    non_empty_cells.append((col_idx, value))
            
            # 如果只有一个单元格有数据，且不在第一列
            if len(non_empty_cells) == 1 and non_empty_cells[0][0] != 0:
                col_idx, value = non_empty_cells[0]
                # 清空原位置
                df.iloc[index, col_idx] = ''
                # 移动到第一列
                df.iloc[index, 0] = value
                moved_count += 1
                # logger.debug(f"行 {index+1}: 将数据从列 {col_idx+1} 移动到列 1")
        
        logger.info(f"移动了 {moved_count} 行的单数据到最左侧")
        return df
    
    def _process_time_amount_format(self, df):
        """处理D列和C列的特殊格式"""
        logger.info("处理时间金额格式...")
        
        # 处理D列：删除xx.xx亿格式
        if len(df.columns) > 3:  # 确保D列存在
            d_col = df.columns[3]
            processed_d = 0
            
            for index, value in df[d_col].items():
                if pd.notna(value):
                    value_str = str(value)
                    # 匹配 xx.xx亿xx:xx:xx 或 xx.xx亿|xx:xx:xx 格式，删除 xx.xx亿 或 xx.xx亿| 部分
                    pattern = r'(\d+\.?\d*亿\|?)(\d{1,2}:\d{2}:\d{2})'
                    match = re.search(pattern, value_str)
                    if match:
                        # 只保留时间部分
                        new_value = match.group(2)
                        df.iloc[index, 3] = new_value
                        processed_d += 1
                        logger.debug(f"D列行 {index+1}: '{value_str}' -> '{new_value}'")
            
            logger.info(f"D列处理了 {processed_d} 个时间金额格式")
        
        # 处理C列：删除xx:xx:xx格式
        if len(df.columns) > 2:  # 确保C列存在
            c_col = df.columns[2]
            processed_c = 0
            
            for index, value in df[c_col].items():
                if pd.notna(value):
                    value_str = str(value)
                    # 匹配 xx.xx亿xx:xx:xx 或 xx.xx亿|xx:xx:xx 格式，删除 |xx:xx:xx 或 xx:xx:xx 部分
                    pattern = r'(\d+\.?\d*亿)(\|?\d{1,2}:\d{2}:\d{2})'
                    match = re.search(pattern, value_str)
                    if match:
                        # 只保留金额部分
                        new_value = match.group(1)
                        df.iloc[index, 2] = new_value
                        processed_c += 1
                        logger.debug(f"C列行 {index+1}: '{value_str}' -> '{new_value}'")
            
            logger.info(f"C列处理了 {processed_c} 个时间金额格式")
        
        return df
    
    def _extract_stock_codes(self, df):
        """提取A列中"汉字+6位数字"格式的数字到新列，并从A列中删除6位数字"""
        logger.info("提取股票代码并从A列删除数字...")

        # 在最后添加新列
        new_col_name = "股票代码"
        df[new_col_name] = ''

        extracted_count = 0
        a_col = df.columns[0]

        for index, value in df[a_col].items():
            if pd.notna(value):
                value_str = str(value)
                # 匹配"汉字+6位数字"格式（汉字可能是3个或4个）
                # [\u4e00-\u9fff] 匹配汉字字符
                pattern = r'([\u4e00-\u9fff]{3,4})(\d{6})'
                match = re.search(pattern, value_str)
                if match:
                    chinese_part = match.group(1)  # 汉字部分
                    code = match.group(2)  # 6位数字

                    # 将6位数字填入新列
                    df.iloc[index, -1] = code

                    # 从A列中删除6位数字，只保留汉字部分
                    df.iloc[index, 0] = chinese_part

                    extracted_count += 1
                    logger.debug(f"A列行 {index+1}: 提取代码 '{code}'，A列更新为 '{chinese_part}'")

        logger.info(f"提取了 {extracted_count} 个股票代码，并从A列删除了数字")
        return df
    
    def _format_asterisk_cells(self, excel_file):
        """标黄加粗含*的单元格"""
        logger.info("格式化含*的单元格...")
        
        try:
            wb = load_workbook(excel_file)
            ws = wb.active
            
            formatted_count = 0
            
            for row in ws.iter_rows():
                for cell in row:
                    if cell.value and '*' in str(cell.value):
                        cell.fill = self.yellow_fill
                        cell.font = self.bold_font
                        formatted_count += 1
                        logger.debug(f"格式化单元格 {cell.coordinate}: '{cell.value}'")
            
            wb.save(excel_file)
            logger.info(f"格式化了 {formatted_count} 个含*的单元格")
            
        except Exception as e:
            logger.error(f"格式化单元格失败: {e}")

def main():
    """主函数"""
    processor = ExcelPostProcessor()
    
    # 查找最新的合并文件
    output_dir = Path("output")
    excel_files = list(output_dir.glob("2025-*.xlsx"))
    
    # 排除temp文件，找到合并文件
    merged_files = [f for f in excel_files if "_temp" not in f.name and "_processed" not in f.name]
    
    if not merged_files:
        logger.error("未找到合并的Excel文件")
        return False
    
    # 使用最新的文件
    input_file = max(merged_files, key=lambda x: x.stat().st_mtime)
    output_file = input_file.parent / f"{input_file.stem}_processed{input_file.suffix}"
    
    logger.info(f"输入文件: {input_file}")
    logger.info(f"输出文件: {output_file}")
    
    success = processor.process_excel_file(input_file, output_file)
    
    if success:
        logger.info("✅ Excel后处理完成")
        return True
    else:
        logger.error("❌ Excel后处理失败")
        return False

if __name__ == "__main__":
    main()

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
股票数据定时任务调度器 - 简化版
支持定时执行股票相关脚本，自动检测当天执行状态
"""

import os
import sys
import json
import time
import signal
import logging
import subprocess
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional

try:
    import schedule
except ImportError:
    print("请安装schedule库: pip install schedule")
    sys.exit(1)


class SimpleStockScheduler:
    """简化的股票数据定时任务调度器"""
    
    def __init__(self, config_file: str = "config.json"):
        """初始化调度器"""
        self.config_file = Path(config_file)
        self.config = self._load_config()
        self.running = False
        self.startup_completed = False
        
        # 设置日志
        self._setup_logging()
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        self.logger.info("📅 股票数据定时任务调度器已初始化")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if not self.config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_file}")
        
        with open(self.config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _setup_logging(self):
        """设置日志"""
        log_file = self.config["settings"]["log_file"]
        
        # 创建日志目录
        log_path = Path(log_file)
        log_path.parent.mkdir(exist_ok=True)
        
        # 配置日志格式
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {signum}，正在停止调度器...")
        self.stop()
    
    def _is_trading_day(self) -> bool:
        """判断是否为交易日（简单实现，排除周末）"""
        if not self.config["settings"]["weekend_skip"]:
            return True
        
        today = datetime.now()
        return today.weekday() < 5  # Monday to Friday
    
    def _get_task_log_key(self, task_name: str, date: str = None) -> str:
        """获取任务日志键"""
        if date is None:
            date = datetime.now().strftime('%Y-%m-%d')
        return f"{task_name}_{date}"
    
    def _is_task_completed_today(self, task_name: str) -> bool:
        """检查任务今天是否已完成"""
        log_key = self._get_task_log_key(task_name)
        log_file = Path(f"{task_name}_status.log")
        
        if not log_file.exists():
            return False
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if log_key in line and "SUCCESS" in line:
                        return True
        except Exception:
            pass
        
        return False
    
    def _mark_task_completed(self, task_name: str, success: bool):
        """标记任务完成状态"""
        log_key = self._get_task_log_key(task_name)
        status = "SUCCESS" if success else "FAILED"
        log_file = Path(f"{task_name}_status.log")
        
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(f"{datetime.now().isoformat()} - {log_key} - {status}\n")
    
    def _execute_script(self, task: Dict[str, Any]) -> bool:
        """执行脚本"""
        try:
            # 构建脚本路径
            project_root = Path(self.config["settings"]["project_root"])
            script_path = project_root / task["script_path"] / task["script_file"]
            
            if not script_path.exists():
                self.logger.error(f"脚本文件不存在: {script_path}")
                return False
            
            # 处理参数中的变量替换
            args = []
            for arg in task.get("args", []):
                if arg == "{today}":
                    args.append(datetime.now().strftime('%Y-%m-%d'))
                else:
                    args.append(arg)
            
            # 构建命令
            command = [sys.executable, str(script_path)] + args
            cwd = script_path.parent
            
            self.logger.info(f"执行命令: {' '.join(command)}")
            self.logger.info(f"工作目录: {cwd}")
            
            # 执行命令
            process = subprocess.Popen(
                command,
                cwd=str(cwd),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8'
            )
            
            # 等待完成
            stdout, stderr = process.communicate(timeout=task.get("timeout", 3600))
            
            # 记录输出
            if stdout:
                self.logger.info(f"[{task['name']}] 输出: {stdout.strip()}")
            if stderr:
                self.logger.error(f"[{task['name']}] 错误: {stderr.strip()}")
            
            # 检查返回码
            success = process.returncode == 0
            if success:
                self.logger.info(f"✅ 任务 {task['name']} 执行成功")
            else:
                self.logger.error(f"❌ 任务 {task['name']} 执行失败，返回码: {process.returncode}")
            
            return success
            
        except subprocess.TimeoutExpired:
            self.logger.error(f"❌ 任务 {task['name']} 执行超时")
            process.kill()
            return False
        except Exception as e:
            self.logger.error(f"❌ 任务 {task['name']} 执行异常: {str(e)}")
            return False
    
    def _execute_task_with_retry(self, task: Dict[str, Any]):
        """带重试机制的任务执行"""
        task_name = task["name"]
        max_retry = self.config["settings"]["max_retry"]
        retry_delay = self.config["settings"]["retry_delay_minutes"]
        
        # 检查是否已完成
        if self._is_task_completed_today(task_name):
            self.logger.info(f"⏭️ 任务 {task_name} 今天已完成，跳过执行")
            return
        
        # 检查是否为交易日
        if not self._is_trading_day():
            self.logger.info(f"⏭️ 非交易日，跳过任务 {task_name}")
            return
        
        self.logger.info(f"🚀 开始执行任务: {task_name} - {task['description']}")
        
        for attempt in range(max_retry + 1):
            if attempt > 0:
                self.logger.info(f"🔄 重试执行任务 {task_name} (第{attempt}次重试)")
                time.sleep(retry_delay * 60)
            
            success = self._execute_script(task)
            
            if success:
                self._mark_task_completed(task_name, True)
                return
        
        # 所有重试都失败
        self.logger.error(f"❌ 任务 {task_name} 执行失败，已达到最大重试次数")
        self._mark_task_completed(task_name, False)

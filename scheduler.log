2025-08-14 15:10:18,664 - INFO - 📅 股票数据定时任务调度器已初始化
2025-08-14 15:10:18,665 - INFO - 🚀 启动股票数据定时任务调度器...
2025-08-14 15:10:18,665 - INFO - ⏰ 开机启动任务将在 5 分钟后执行
2025-08-14 15:10:18,667 - INFO - 📅 已调度每日任务: 15:10 - fupan_data
2025-08-14 15:10:18,667 - INFO - 📅 已调度每日任务: 16:00 - kline_data
2025-08-14 15:10:18,667 - INFO - 📅 已调度每日任务: 16:30 - stock_select
2025-08-14 15:10:18,667 - INFO - 📅 已调度每日任务: 17:10 - thsfp_main
2025-08-14 15:10:18,667 - INFO - 📋 当前调度任务:
2025-08-14 15:10:18,667 - INFO -   ⏰ startup+5min: env_update - 更新stock_env环境的所有pip包
2025-08-14 15:10:18,668 - INFO -   ⏰ 15:10: fupan_data - 获取当天复盘数据（涨停精确查询和市场分析）
2025-08-14 15:10:18,668 - INFO -   ⏰ 16:00: kline_data - 获取股票K线数据
2025-08-14 15:10:18,668 - INFO -   ⏰ 16:30: stock_select - 执行选股程序
2025-08-14 15:10:18,668 - INFO -   ⏰ 17:10: thsfp_main - 执行同花顺数据处理（爬取+OCR+后处理+清理）
2025-08-14 15:10:18,668 - INFO - ✅ 调度器启动完成，等待任务执行...
2025-08-14 15:15:18,667 - INFO - 🚀 开始执行任务: env_update - 更新stock_env环境的所有pip包
2025-08-14 15:15:18,667 - INFO - 执行命令: C:\Users\<USER>\.conda\envs\stock_env\python.exe d:\Documents\Code\stock\env\3_update_packages.py --all --no-backup
2025-08-14 15:15:18,668 - INFO - 工作目录: d:\Documents\Code\stock\env
2025-08-14 15:15:20,512 - ERROR - [env_update] 错误: Traceback (most recent call last):
  File "d:\Documents\Code\stock\env\3_update_packages.py", line 390, in <module>
    main()
  File "d:\Documents\Code\stock\env\3_update_packages.py", line 355, in main
    current_env = check_stock_env()
                  ^^^^^^^^^^^^^^^^^
  File "d:\Documents\Code\stock\env\3_update_packages.py", line 51, in check_stock_env
    print(f"\u2713 ǰ: {current_env}")
UnicodeEncodeError: 'gbk' codec can't encode character '\u2713' in position 0: illegal multibyte sequence
2025-08-14 15:15:20,512 - ERROR - ❌ 任务 env_update 执行失败，返回码: 1
2025-08-14 15:15:20,513 - INFO - 🔄 重试执行任务 env_update (第1次重试)
2025-08-14 15:25:20,515 - INFO - 执行命令: C:\Users\<USER>\.conda\envs\stock_env\python.exe d:\Documents\Code\stock\env\3_update_packages.py --all --no-backup
2025-08-14 15:25:20,516 - INFO - 工作目录: d:\Documents\Code\stock\env
2025-08-14 15:25:22,307 - ERROR - [env_update] 错误: Traceback (most recent call last):
  File "d:\Documents\Code\stock\env\3_update_packages.py", line 390, in <module>
    main()
  File "d:\Documents\Code\stock\env\3_update_packages.py", line 355, in main
    current_env = check_stock_env()
                  ^^^^^^^^^^^^^^^^^
  File "d:\Documents\Code\stock\env\3_update_packages.py", line 51, in check_stock_env
    print(f"\u2713 ǰ: {current_env}")
UnicodeEncodeError: 'gbk' codec can't encode character '\u2713' in position 0: illegal multibyte sequence
2025-08-14 15:25:22,308 - ERROR - ❌ 任务 env_update 执行失败，返回码: 1
2025-08-14 15:25:22,308 - INFO - 🔄 重试执行任务 env_update (第2次重试)
2025-08-14 15:35:22,310 - INFO - 执行命令: C:\Users\<USER>\.conda\envs\stock_env\python.exe d:\Documents\Code\stock\env\3_update_packages.py --all --no-backup
2025-08-14 15:35:22,310 - INFO - 工作目录: d:\Documents\Code\stock\env
2025-08-14 15:35:24,078 - ERROR - [env_update] 错误: Traceback (most recent call last):
  File "d:\Documents\Code\stock\env\3_update_packages.py", line 390, in <module>
    main()
  File "d:\Documents\Code\stock\env\3_update_packages.py", line 355, in main
    current_env = check_stock_env()
                  ^^^^^^^^^^^^^^^^^
  File "d:\Documents\Code\stock\env\3_update_packages.py", line 51, in check_stock_env
    print(f"\u2713 ǰ: {current_env}")
UnicodeEncodeError: 'gbk' codec can't encode character '\u2713' in position 0: illegal multibyte sequence
2025-08-14 15:35:24,080 - ERROR - ❌ 任务 env_update 执行失败，返回码: 1
2025-08-14 15:35:24,080 - INFO - 🔄 重试执行任务 env_update (第3次重试)

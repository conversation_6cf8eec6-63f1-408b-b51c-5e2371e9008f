#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
涨停精确查询自动化脚本
支持命令行参数，用于定时任务调用
"""

import sys
import os
from datetime import datetime, date
from pathlib import Path

# 添加当前目录到路径
sys.path.append(str(Path(__file__).parent))

# 导入原始脚本的函数
try:
    from daily_limit_precisequery import (
        get_trade_dates, is_trade_date, process_single_date, 
        get_default_file_path, sort_worksheets_by_date
    )
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)


def get_today_date():
    """获取今天的日期"""
    return datetime.now().date()


def get_latest_trade_date():
    """获取最近的交易日"""
    try:
        today = get_today_date()
        
        # 检查今天是否为交易日
        if is_trade_date(today):
            return today
        
        # 如果今天不是交易日，往前找最近的交易日
        trade_dates_df = get_trade_dates()
        if trade_dates_df.empty:
            print("❌ 无法获取交易日历数据")
            return None
        
        # 获取今天之前的最近交易日
        trade_dates_df['trade_date'] = trade_dates_df['trade_date'].dt.date
        recent_dates = trade_dates_df[trade_dates_df['trade_date'] <= today]
        
        if recent_dates.empty:
            print("❌ 未找到最近的交易日")
            return None
        
        latest_date = recent_dates['trade_date'].max()
        return latest_date
        
    except Exception as e:
        print(f"❌ 获取最近交易日失败: {e}")
        return None


def main():
    """主函数 - 自动获取当日或最近交易日数据"""
    try:
        print("🚀 涨停精确查询自动化脚本启动")
        print("=" * 50)
        
        # 解析命令行参数
        target_date = None
        if len(sys.argv) > 1:
            date_str = sys.argv[1]
            try:
                if date_str.lower() == "today":
                    target_date = get_latest_trade_date()
                else:
                    # 尝试解析日期格式 YYYY-MM-DD 或 YYYYMMDD
                    if len(date_str) == 8 and date_str.isdigit():
                        target_date = datetime.strptime(date_str, '%Y%m%d').date()
                    elif len(date_str) == 10:
                        target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                    else:
                        raise ValueError("日期格式不正确")
            except ValueError as e:
                print(f"❌ 日期参数错误: {e}")
                print("用法: python daily_limit_precisequery_auto.py [today|YYYY-MM-DD|YYYYMMDD]")
                return False
        else:
            # 默认获取最近交易日
            target_date = get_latest_trade_date()
        
        if not target_date:
            print("❌ 无法确定查询日期")
            return False
        
        # 检查是否为交易日
        if not is_trade_date(target_date):
            print(f"⚠️ {target_date.strftime('%Y-%m-%d')} 不是交易日")
            return False
        
        print(f"📅 查询日期: {target_date.strftime('%Y-%m-%d')}")
        
        # 获取默认Excel文件路径
        file_path = get_default_file_path()
        if not file_path:
            print("❌ 无法获取默认文件路径")
            return False
        
        print(f"📁 Excel文件: {file_path}")
        
        # 执行查询
        print("\n🔍 开始查询涨停数据...")
        success = process_single_date(target_date, file_path)
        
        if success:
            print(f"✅ {target_date.strftime('%Y-%m-%d')} 涨停数据查询成功")
            print(f"📊 数据已保存到: {file_path}")
            return True
        else:
            print(f"❌ {target_date.strftime('%Y-%m-%d')} 涨停数据查询失败")
            return False
            
    except Exception as e:
        print(f"❌ 程序执行失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

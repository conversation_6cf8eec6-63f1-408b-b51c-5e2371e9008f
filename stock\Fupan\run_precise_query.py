#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
涨停精确查询包装脚本
通过subprocess调用原始脚本并提供自动输入
"""

import sys
import subprocess
import os
from pathlib import Path

def main():
    """主函数"""
    try:
        print("启动涨停精确查询...")
        
        # 获取原始脚本路径
        script_dir = Path(__file__).parent
        original_script = script_dir / "daily_limit_precisequery.py"
        
        if not original_script.exists():
            print(f"错误: 找不到脚本文件 {original_script}")
            return False
        
        # 准备自动输入：选择单日查询(1)，然后使用默认日期(回车)
        input_data = "1\n\n"
        
        # 设置环境变量以避免编码问题
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        
        # 执行原始脚本
        process = subprocess.Popen(
            [sys.executable, str(original_script)],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            errors='replace',  # 替换无法编码的字符
            cwd=str(script_dir),
            env=env
        )
        
        try:
            stdout, stderr = process.communicate(input=input_data, timeout=300)
            
            # 输出结果（过滤掉可能的编码问题）
            if stdout:
                # 只输出关键信息，避免Unicode问题
                lines = stdout.split('\n')
                for line in lines:
                    if any(keyword in line for keyword in ['成功', '失败', '完成', '错误', '数据']):
                        try:
                            print(line.encode('ascii', 'ignore').decode('ascii'))
                        except:
                            print("处理完成")
            
            if process.returncode == 0:
                print("涨停精确查询任务执行成功")
                return True
            else:
                print("涨停精确查询任务执行失败")
                return False
                
        except subprocess.TimeoutExpired:
            print("任务执行超时")
            process.kill()
            return False
            
    except Exception as e:
        print(f"执行失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

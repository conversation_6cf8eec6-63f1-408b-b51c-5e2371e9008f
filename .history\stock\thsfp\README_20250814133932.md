# 股票数据处理系统

这是一个完整的自动化股票数据处理系统，能够从网页爬取股票相关图片，进行OCR识别，生成Excel文件，并进行后处理和清理。

## 🚀 功能特点

1. **网页爬取**: 自动从指定网站爬取股票相关图片
2. **智能图像切分**: 基于膨胀算法精确检测表格横线，在横线位置进行切分
3. **OCR识别**: 使用先进的表格识别技术，准确提取表格数据
4. **数据合并**: 自动合并多个表格，生成统一的Excel文件
5. **Excel后处理**: 自动格式化、提取股票代码、处理时间金额格式
6. **文件清理**: 自动清理中间文件，只保留必要的输入和输出文件

## 📋 使用方法

### 完整工作流程（推荐）
```bash
python main_controller.py full
```
执行完整流程：爬取→处理→后处理→清理

### 其他命令选项
```bash
python main_controller.py basic      # 仅基础流程（爬取+OCR+合并）
python main_controller.py no-cleanup # 完整流程但不清理中间文件
python main_controller.py post       # 仅运行Excel后处理
python main_controller.py cleanup    # 仅运行文件清理
```

### 默认行为
直接运行 `python main_controller.py` 等同于 `python main_controller.py full`

## 📁 文件结构

### 输入文件
- `images/`: 存储原始图片文件
  - `2025-08-13-1.png` - 第一张图片
  - `2025-08-13-2.png` - 第二张图片

### 输出文件
- `output/`: 存储最终处理结果
  - `2025-08-13_processed.xlsx` - 最终处理后的Excel文件

### 中间文件（运行时临时生成，完成后自动清理）
- `images/split_images/` - 图片切分结果
- `output/*_temp.xlsx` - 临时Excel文件
- `output/2025-*.xlsx` - 中间合并文件
- `debug_images/` - 调试图像

## 🔧 技术特点

### 图像切分算法
- **膨胀算法**: 精确检测表格横线
- **预估切分**: 根据图片高度预估切分数量+2，确保片段小于1500px
- **横线优先**: 100%在横线位置切分，偏差控制在合理范围内

### Excel后处理功能
1. **删除unnamed单元格**: 清理无效数据
2. **单数据行移动**: 将单独数据移到最左侧
3. **含*单元格格式化**: 标黄加粗
4. **时间金额格式处理**:
   - D列删除`xx.xx亿`或`xx.xx亿|`，保留时间
   - C列删除`|xx:xx:xx`或`xx:xx:xx`，保留金额
5. **股票代码提取**: 从A列提取"汉字+6位数字"中的数字到新列，并从A列删除

## 📊 处理效果

### 图像切分
- **第一张图**: 9953px → 9个片段（1031-1258px）
- **第二张图**: 12087px → 11个片段（1004-1216px）
- **切分精度**: 100%在横线位置，偏差6-139像素

### 数据处理
- **第一张图**: 48行数据
- **第二张图**: 72行数据
- **合并后**: 118行 x 8列（含股票代码列）
- **后处理**: 提取92个股票代码，格式化14个含*单元格

## 🛠️ 依赖环境

```bash
pip install opencv-python pandas requests beautifulsoup4 rapidocr_onnxruntime wired-table-rec openpyxl
```

## 📝 日志记录

系统会自动生成 `workflow.log` 文件记录详细的处理过程，便于调试和监控。

## ⚡ 性能优化

- 日志输出已优化，只显示关键信息
- 自动清理中间文件，节省存储空间
- 支持命令行参数，灵活控制执行流程

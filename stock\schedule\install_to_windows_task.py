#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
将股票定时任务调度器安装到Windows计划任务
支持开机自启动和用户登录时启动
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def check_windows():
    """检查是否为Windows系统"""
    if platform.system() != 'Windows':
        print("❌ 此脚本仅支持Windows操作系统")
        return False
    
    print(f"✅ 操作系统检查通过: {platform.system()} {platform.release()}")
    return True


def check_admin_privileges():
    """检查是否有管理员权限"""
    try:
        import ctypes
        if not ctypes.windll.shell32.IsUserAnAdmin():
            print("❌ 需要管理员权限，请以管理员身份运行此脚本")
            return False
        print("✅ 管理员权限检查通过")
        return True
    except:
        print("⚠️ 无法检查管理员权限，继续执行...")
        return True


def get_script_paths():
    """获取脚本路径"""
    current_dir = Path(__file__).parent.absolute()
    python_exe = sys.executable
    scheduler_script = current_dir / "simple_scheduler.py"
    config_file = current_dir / "config.json"
    
    if not scheduler_script.exists():
        print(f"❌ 调度器脚本不存在: {scheduler_script}")
        return None, None, None
    
    if not config_file.exists():
        print(f"❌ 配置文件不存在: {config_file}")
        return None, None, None
    
    return python_exe, scheduler_script, config_file


def create_windows_task():
    """创建Windows计划任务"""
    python_exe, scheduler_script, config_file = get_script_paths()
    if not all([python_exe, scheduler_script, config_file]):
        return False
    
    # 任务名称
    task_name = "StockScheduler"
    
    # 构建命令
    command = f'"{python_exe}" "{scheduler_script}" --config "{config_file}"'
    
    # 创建计划任务的XML配置
    xml_config = f'''<?xml version="1.0" encoding="UTF-16"?>
<Task version="1.2" xmlns="http://schemas.microsoft.com/windows/2004/02/mit/task">
  <RegistrationInfo>
    <Date>2024-01-01T00:00:00</Date>
    <Author>StockScheduler</Author>
    <Description>股票数据定时任务调度器</Description>
  </RegistrationInfo>
  <Triggers>
    <LogonTrigger>
      <Enabled>true</Enabled>
      <Delay>PT1M</Delay>
    </LogonTrigger>
  </Triggers>
  <Principals>
    <Principal id="Author">
      <LogonType>InteractiveToken</LogonType>
      <RunLevel>LeastPrivilege</RunLevel>
    </Principal>
  </Principals>
  <Settings>
    <MultipleInstancesPolicy>IgnoreNew</MultipleInstancesPolicy>
    <DisallowStartIfOnBatteries>false</DisallowStartIfOnBatteries>
    <StopIfGoingOnBatteries>false</StopIfGoingOnBatteries>
    <AllowHardTerminate>true</AllowHardTerminate>
    <StartWhenAvailable>true</StartWhenAvailable>
    <RunOnlyIfNetworkAvailable>false</RunOnlyIfNetworkAvailable>
    <IdleSettings>
      <StopOnIdleEnd>false</StopOnIdleEnd>
      <RestartOnIdle>false</RestartOnIdle>
    </IdleSettings>
    <AllowStartOnDemand>true</AllowStartOnDemand>
    <Enabled>true</Enabled>
    <Hidden>false</Hidden>
    <RunOnlyIfIdle>false</RunOnlyIfIdle>
    <WakeToRun>false</WakeToRun>
    <ExecutionTimeLimit>PT0S</ExecutionTimeLimit>
    <Priority>7</Priority>
  </Settings>
  <Actions>
    <Exec>
      <Command>{python_exe}</Command>
      <Arguments>"{scheduler_script}" --config "{config_file}"</Arguments>
      <WorkingDirectory>{scheduler_script.parent}</WorkingDirectory>
    </Exec>
  </Actions>
</Task>'''
    
    # 保存XML配置到临时文件
    xml_file = Path("stock_scheduler_task.xml")
    try:
        with open(xml_file, 'w', encoding='utf-16') as f:
            f.write(xml_config)
        
        print(f"📝 创建任务配置文件: {xml_file}")
        
        # 删除已存在的任务（如果有）
        try:
            subprocess.run([
                "schtasks", "/delete", "/tn", task_name, "/f"
            ], capture_output=True, check=False)
        except:
            pass
        
        # 创建新任务
        print(f"📅 创建Windows计划任务: {task_name}")
        result = subprocess.run([
            "schtasks", "/create", "/tn", task_name, "/xml", str(xml_file)
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ Windows计划任务创建成功: {task_name}")
            print(f"   任务将在用户登录1分钟后自动启动")
            print(f"   命令: {command}")
            return True
        else:
            print(f"❌ Windows计划任务创建失败:")
            print(f"   错误信息: {result.stderr}")
            return False
    
    except Exception as e:
        print(f"❌ 创建Windows计划任务时发生异常: {e}")
        return False
    
    finally:
        # 清理临时文件
        if xml_file.exists():
            try:
                xml_file.unlink()
            except:
                pass


def show_task_info():
    """显示任务信息"""
    task_name = "StockScheduler"
    
    try:
        result = subprocess.run([
            "schtasks", "/query", "/tn", task_name, "/fo", "list", "/v"
        ], capture_output=True, text=True, encoding='gbk')
        
        if result.returncode == 0:
            print("\n📋 任务信息:")
            print(result.stdout)
        else:
            print(f"❌ 查询任务信息失败: {result.stderr}")
    
    except Exception as e:
        print(f"❌ 查询任务信息时发生异常: {e}")


def uninstall_task():
    """卸载Windows计划任务"""
    task_name = "StockScheduler"
    
    try:
        print(f"🗑️ 删除Windows计划任务: {task_name}")
        result = subprocess.run([
            "schtasks", "/delete", "/tn", task_name, "/f"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ Windows计划任务删除成功: {task_name}")
            return True
        else:
            print(f"❌ Windows计划任务删除失败: {result.stderr}")
            return False
    
    except Exception as e:
        print(f"❌ 删除Windows计划任务时发生异常: {e}")
        return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="股票定时任务调度器 - Windows计划任务安装工具")
    parser.add_argument('action', choices=['install', 'uninstall', 'info'], 
                       help='操作类型: install=安装, uninstall=卸载, info=查看信息')
    
    args = parser.parse_args()
    
    print("🚀 股票定时任务调度器 - Windows计划任务安装工具")
    print("=" * 60)
    
    # 检查系统要求
    if not check_windows():
        sys.exit(1)
    
    if args.action in ['install', 'uninstall']:
        if not check_admin_privileges():
            sys.exit(1)
    
    if args.action == 'install':
        success = create_windows_task()
        if success:
            print("\n💡 使用说明:")
            print("1. 任务已设置为用户登录时自动启动")
            print("2. 可以在'任务计划程序'中查看和管理任务")
            print("3. 任务名称: StockScheduler")
            print("4. 重启计算机或重新登录后任务将自动运行")
            print("\n📖 其他操作:")
            print("- 查看任务信息: python install_to_windows_task.py info")
            print("- 卸载任务: python install_to_windows_task.py uninstall")
        sys.exit(0 if success else 1)
    
    elif args.action == 'uninstall':
        success = uninstall_task()
        sys.exit(0 if success else 1)
    
    elif args.action == 'info':
        show_task_info()


if __name__ == "__main__":
    main()

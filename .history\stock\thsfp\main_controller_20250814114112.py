#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主控制程序
整合网页爬取、图片处理和Excel处理的完整流程
"""

import logging
from pathlib import Path
import time

# 导入各个模块
from web_scraper import WebScraper
from adaptive_table_processor import AdaptiveTableProcessor


# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MainController:
    """主控制器"""
    
    def __init__(self):
        self.current_dir = Path(__file__).parent
        self.images_dir = self.current_dir / "images"
        self.output_dir = self.current_dir / "output"
        
        logger.info("MainController 初始化完成")

    def merge_excel_files(self, excel_files, output_path):
        """合并多个Excel文件"""
        try:
            import pandas as pd

            logger.info(f"开始合并 {len(excel_files)} 个Excel文件")

            all_data = []

            for excel_file in excel_files:
                try:
                    df = pd.read_excel(excel_file)
                    if not df.empty:
                        all_data.append(df)
                        logger.info(f"读取文件: {excel_file}, 行数: {len(df)}")
                    else:
                        logger.warning(f"文件为空: {excel_file}")
                except Exception as e:
                    logger.error(f"读取文件失败 {excel_file}: {e}")

            if not all_data:
                logger.error("没有有效的数据可以合并")
                return False

            # 合并所有数据
            merged_df = pd.concat(all_data, ignore_index=True)

            # 保存合并后的文件
            merged_df.to_excel(output_path, index=False)

            logger.info(f"Excel文件合并完成: {output_path}, 总行数: {len(merged_df)}")
            return True

        except Exception as e:
            logger.error(f"合并Excel文件失败: {e}")
            return False

    def run_complete_workflow(self):
        """运行完整的工作流程"""
        try:
            logger.info("="*80)
            logger.info("🚀 开始完整的数据处理工作流程")
            logger.info("="*80)
            
            start_time = time.time()
            
            # 第1步：检查现有图片
            logger.info("📋 第1步：检查现有图片文件")
            images_dir = Path("images")
            scraped_images = []

            # 查找现有的图片文件
            for img_file in images_dir.glob("*.png"):
                if img_file.name.startswith("2025-08"):
                    scraped_images.append(img_file)

            if not scraped_images:
                logger.error("❌ 未找到图片文件，无法继续")
                return False

            logger.info(f"✅ 找到 {len(scraped_images)} 个图片文件: {[img.name for img in scraped_images]}")
            
            # 第2步：图片处理
            logger.info("📋 第2步：处理图片并生成Excel")
            processed_count = 0
            excel_files = []

            # 初始化自适应表格处理器
            processor = AdaptiveTableProcessor()

            # 按日期分组处理图片
            from collections import defaultdict
            date_groups = defaultdict(list)

            for image_path in scraped_images:
                image_name = Path(image_path).stem
                # 提取日期部分（假设格式为 2025-08-13-1, 2025-08-13-2）
                date_part = '-'.join(image_name.split('-')[:3])  # 取前3部分作为日期
                date_groups[date_part].append(image_path)

            for date, images in date_groups.items():
                logger.info(f"处理日期 {date} 的 {len(images)} 张图片")

                if len(images) == 1:
                    # 单张图片，直接处理
                    image_path = images[0]
                    logger.info(f"处理单张图片: {Path(image_path).name}")

                    output_path = self.output_dir / f"{date}.xlsx"
                    success = processor.process_complete_workflow(image_path, output_path)

                    if success:
                        processed_count += 1
                        excel_files.append(output_path)
                        logger.info(f"✅ 图片处理成功: {output_path}")
                    else:
                        logger.error(f"❌ 图片处理失败: {image_path}")

                else:
                    # 多张图片，需要合并
                    logger.info(f"处理多张图片: {[Path(img).name for img in images]}")
                    temp_excel_files = []

                    # 先分别处理每张图片
                    for i, image_path in enumerate(images, 1):
                        temp_output = self.output_dir / f"{date}-{i}_temp.xlsx"
                        success = processor.process_complete_workflow(image_path, temp_output)

                        if success:
                            temp_excel_files.append(temp_output)
                            logger.info(f"✅ 临时文件生成: {temp_output}")
                        else:
                            logger.error(f"❌ 图片处理失败: {image_path}")

                    # 合并Excel文件
                    if temp_excel_files:
                        merged_output = self.output_dir / f"{date}.xlsx"
                        success = self.merge_excel_files(temp_excel_files, merged_output)

                        if success:
                            processed_count += len(images)
                            excel_files.append(merged_output)
                            logger.info(f"✅ Excel文件合并成功: {merged_output}")

                            # 保留临时文件供检查
                            logger.info(f"📋 保留临时文件供检查:")
                            for temp_file in temp_excel_files:
                                logger.info(f"   📄 {temp_file}")
                        else:
                            logger.error(f"❌ Excel文件合并失败: {date}")

            if processed_count == 0:
                logger.error("❌ 所有图片处理失败，无法继续")
                return False

            logger.info(f"✅ 图片处理完成，成功处理 {processed_count} 张图片，生成 {len(excel_files)} 个Excel文件")
            
            # 处理完成
            logger.info(f"✅ 数据处理完成，生成 {len(excel_files)} 个Excel文件")
            
            # 统计信息
            total_time = time.time() - start_time
            
            logger.info("="*80)
            logger.info("🎉 完整工作流程执行完成！")
            logger.info(f"📊 处理统计:")
            logger.info(f"   爬取图片数: {len(scraped_images)}")
            logger.info(f"   成功处理图片: {processed_count}")
            logger.info(f"   生成Excel文件: {len(excel_files)}")
            logger.info(f"   总处理时间: {total_time:.1f}秒")
            logger.info("📁 输出文件:")
            for file_path in excel_files:
                logger.info(f"   📄 {file_path}")
            logger.info("="*80)
            
            return True
            
        except Exception as e:
            logger.error(f"完整工作流程执行失败: {e}")
            return False

def main():
    """主函数"""
    try:
        controller = MainController()
        success = controller.run_complete_workflow()
        
        if success:
            logger.info("🎉 所有任务执行成功！")
        else:
            logger.error("❌ 任务执行失败")
        
    except Exception as e:
        logger.error(f"主程序执行失败: {e}")

if __name__ == "__main__":
    main()

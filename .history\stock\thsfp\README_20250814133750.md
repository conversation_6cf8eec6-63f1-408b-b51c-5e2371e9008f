# 股票数据处理系统

这是一个完整的自动化股票数据处理系统，能够从网页爬取股票相关图片，进行OCR识别，生成Excel文件，并进行后处理和清理。

## 🚀 功能特点

1. **网页爬取**: 自动从指定网站爬取股票相关图片
2. **智能图像切分**: 基于膨胀算法精确检测表格横线，在横线位置进行切分
3. **OCR识别**: 使用先进的表格识别技术，准确提取表格数据
4. **数据合并**: 自动合并多个表格，生成统一的Excel文件
5. **Excel后处理**: 自动格式化、提取股票代码、处理时间金额格式
6. **文件清理**: 自动清理中间文件，只保留必要的输入和输出文件

## 核心算法

### 自适应切分算法
1. **尺寸分析**：根据图像尺寸和最大处理尺寸计算最优切分块数
2. **线条检测**：使用形态学操作和霍夫变换检测粗横线
3. **智能切分**：结合理想切分位置和实际线条位置进行精准切分
4. **缩放优化**：确保每个片段的缩放比例满足识别要求

## 文件结构

```
thsfp/
├── adaptive_table_processor.py  # 自适应表格处理器（核心模块）
├── web_scraper.py              # 网页爬取模块
├── excel_processor.py          # Excel处理模块
├── main_controller.py          # 主控制器
├── images/                    # 图片存储目录
├── output/                    # 输出Excel文件目录
└── README.md                 # 说明文档
```

### 核心模块说明

1. **adaptive_table_processor.py** - 自适应表格处理器（核心）
   - 根据图像尺寸自动计算最优切分块数
   - 基于粗横线检测进行精准切分
   - 使用有线表格识别引擎（wired_table_rec_v2）
   - 自动合并多个片段并输出Excel文件

2. **main_controller.py** - 主控制程序
   - 整合所有处理步骤的完整工作流程
   - 协调各个模块的执行顺序
   - 提供统一的入口点

3. **web_scraper.py** - 网页爬取器
   - 从指定网站获取涨停复盘文章
   - 自动识别并下载最大的图片（主图）
   - 从网页内容中提取日期信息
   - 按日期格式（YYYY-MM-DD）命名保存图片

4. **excel_processor.py** - Excel处理器
   - 检测股票名称和6位数代码的混合单元格
   - 自动分离股票名称和代码到不同列
   - 将单格内容移动到第一列
   - 自动调整Excel格式和列宽

## 依赖库

```bash
pip install opencv-python numpy pandas openpyxl beautifulsoup4 requests Pillow
pip install rapidocr-onnxruntime table-cls wired-table-rec
```

## 使用方法

### 1. 直接运行主程序（推荐）

```bash
python adaptive_table_processor.py
```

### 2. 编程调用

```python
from adaptive_table_processor import AdaptiveTableProcessor

# 初始化处理器
processor = AdaptiveTableProcessor()

# 处理图片
success = processor.process_complete_workflow(
    image_path="images/your_image.png",
    output_excel_path="output/result.xlsx"
)
```

### 3. 完整工作流程（包含网页爬取）

```bash
python main_controller.py
```

这将执行完整的工作流程：

1. 爬取涨停复盘网页图片
2. 处理图片生成Excel文件
3. 整理Excel数据格式

## 配置参数

在 `AdaptiveTableProcessor` 类中可以调整以下参数：

- `max_dimension`: 最大处理尺寸（默认1500px）
- `min_scale_factor`: 最小缩放比例（默认1.2）
- `切分精度`: 偏差控制在20像素以内

## 处理效果

- **切分精度**：基于粗横线的智能切分，避免表格内容被错误分割
- **识别准确率**：结合OCR和表格结构识别，提高数据提取准确性
- **处理速度**：自适应切分减少处理时间，提高整体效率
- **输出质量**：生成格式化的Excel文件，支持自动列宽调整
- **多图合并**：自动合并同日期的多张图片为单个Excel文件
- **分割保存**：分割的图片自动保存到split_images文件夹供查看

## 技术参数

| 参数 | 值 | 说明 |
|------|----|----|
| 最大处理尺寸 | 1500px | 表格识别引擎最佳处理尺寸 |
| 最小缩放比例 | 0.9 | 确保图片质量的最小缩放比例 |
| 识别引擎 | wired_table_rec_v2 | 有线表格识别，精度最高 |
| OCR引擎 | RapidOCR | 高速文字识别引擎 |

## 新功能特性

### 多图片合并
- 当存在多个同日期图片（如2025-08-13-1.png, 2025-08-13-2.png）时，系统会自动：
  1. 分别处理每张图片生成临时Excel文件
  2. 将所有临时文件合并为单个Excel文件（如2025-08-13.xlsx）
  3. 自动删除临时文件，保持目录整洁

### 分割图片保存
- 所有分割的图片片段会自动保存到 `images/split_images/` 目录
- 按原图片名称创建子目录（如 `2025-08-13-1/`）
- 每个片段命名为 `segment_01.png`, `segment_02.png` 等
- 便于查看和验证分割效果

## 注意事项

1. **图片质量**：建议使用清晰的图片，分辨率不低于1000px
2. **表格格式**：适用于有明显边框线的表格
3. **内存使用**：大图片处理时会占用较多内存
4. **首次运行**：会自动下载模型文件，需要网络连接
5. **分割图片**：分割图片会占用额外磁盘空间，可定期清理split_images目录

## 故障排除

1. **模型下载失败**：检查网络连接，或手动下载模型文件
2. **识别效果差**：检查图片质量，调整预处理参数
3. **内存不足**：减小 `max_dimension` 参数值
4. **切分不准确**：检查图片中的横线是否清晰

## 示例输出

处理完成后会生成如下统计信息：

```
🎉 自适应表格处理完成！
📊 处理统计:
   切分片段数: 6
   成功处理片段: 6
   总单元格数: 313
   总OCR文本框: 926
   最终表格规模: 48 行 x 7 列
   总处理时间: 90.909秒
   Excel文件: output/result.xlsx
```

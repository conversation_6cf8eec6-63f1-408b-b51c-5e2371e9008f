2025-08-14 15:04:25,854 - INFO - 📅 股票数据定时任务调度器已初始化
2025-08-14 15:04:25,854 - INFO - 🚀 立即执行任务: env_update
2025-08-14 15:04:25,855 - INFO - 🚀 开始执行任务: env_update - 更新stock_env环境的所有pip包
2025-08-14 15:04:25,855 - INFO - 执行命令: C:\Users\<USER>\.conda\envs\stock_env\python.exe d:\Documents\Code\stock\env\3_update_packages.py --all --no-backup
2025-08-14 15:04:25,855 - INFO - 工作目录: d:\Documents\Code\stock\env
2025-08-14 15:04:27,704 - ERROR - ❌ 任务 env_update 执行失败，返回码: 1
2025-08-14 15:04:27,704 - INFO - 🔄 重试执行任务 env_update (第1次重试)
2025-08-14 15:05:01,544 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:05:01,544 - INFO - 🛑 调度器已停止
2025-08-14 15:05:12,746 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:05:12,746 - INFO - 🛑 调度器已停止
2025-08-14 15:06:12,091 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:06:12,092 - INFO - 🛑 调度器已停止
2025-08-14 15:06:23,795 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:06:23,796 - INFO - 🛑 调度器已停止
2025-08-14 15:07:13,159 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:07:13,160 - INFO - 🛑 调度器已停止
2025-08-14 15:07:26,466 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:07:26,468 - INFO - 🛑 调度器已停止
2025-08-14 15:08:14,399 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:08:14,399 - INFO - 🛑 调度器已停止
2025-08-14 15:08:24,689 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:08:24,689 - INFO - 🛑 调度器已停止
2025-08-14 15:08:55,893 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:08:55,895 - INFO - 🛑 调度器已停止
2025-08-14 15:11:27,899 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:11:27,899 - INFO - 🛑 调度器已停止
2025-08-14 15:11:57,802 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:11:57,803 - INFO - 🛑 调度器已停止
2025-08-14 15:14:27,706 - INFO - 执行命令: C:\Users\<USER>\.conda\envs\stock_env\python.exe d:\Documents\Code\stock\env\3_update_packages.py --all --no-backup
2025-08-14 15:14:27,706 - INFO - 工作目录: d:\Documents\Code\stock\env
2025-08-14 15:14:29,446 - ERROR - ❌ 任务 env_update 执行失败，返回码: 1
2025-08-14 15:14:29,446 - INFO - 🔄 重试执行任务 env_update (第2次重试)
2025-08-14 15:16:28,424 - INFO - 📅 股票数据定时任务调度器已初始化
2025-08-14 15:16:28,424 - INFO - 🚀 立即执行任务: precise_query
2025-08-14 15:16:28,425 - INFO - 🚀 开始执行任务: precise_query - 获取当日涨停精确查询数据
2025-08-14 15:16:28,425 - INFO - 执行命令: C:\Users\<USER>\.conda\envs\stock_env\python.exe d:\Documents\Code\stock\Fupan\daily_limit_precisequery_auto.py today
2025-08-14 15:16:28,425 - INFO - 工作目录: d:\Documents\Code\stock\Fupan
2025-08-14 15:16:29,821 - ERROR - [precise_query] 错误: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\py_mini_racer\py_mini_racer.py:13: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
Traceback (most recent call last):
  File "d:\Documents\Code\stock\Fupan\daily_limit_precisequery_auto.py", line 66, in main
    print("\U0001f680 ͣȷѯԶű")
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f680' in position 0: illegal multibyte sequence

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "d:\Documents\Code\stock\Fupan\daily_limit_precisequery_auto.py", line 131, in <module>
    success = main()
              ^^^^^^
  File "d:\Documents\Code\stock\Fupan\daily_limit_precisequery_auto.py", line 124, in main
    print(f"\u274c ִʧ: {str(e)}")
UnicodeEncodeError: 'gbk' codec can't encode character '\u274c' in position 0: illegal multibyte sequence
2025-08-14 15:16:29,821 - ERROR - ❌ 任务 precise_query 执行失败，返回码: 1
2025-08-14 15:16:29,821 - INFO - 🔄 重试执行任务 precise_query (第1次重试)
2025-08-14 15:18:17,367 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:18:17,367 - INFO - 🛑 调度器已停止
2025-08-14 15:18:30,006 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:18:30,007 - INFO - 🛑 调度器已停止
2025-08-14 15:20:19,418 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:20:19,420 - INFO - 🛑 调度器已停止
2025-08-14 15:21:47,559 - INFO - 📅 股票数据定时任务调度器已初始化
2025-08-14 15:21:47,559 - INFO - 🚀 立即执行任务: precise_query
2025-08-14 15:21:47,559 - INFO - 🚀 开始执行任务: precise_query - 获取当日涨停精确查询数据
2025-08-14 15:21:47,560 - INFO - 执行命令: C:\Users\<USER>\.conda\envs\stock_env\python.exe d:\Documents\Code\stock\Fupan\daily_limit_precisequery_auto.py today
2025-08-14 15:21:47,560 - INFO - 工作目录: d:\Documents\Code\stock\Fupan
2025-08-14 15:21:49,110 - INFO - [precise_query] 输出: ͣȷѯԶű
==================================================
ڻȡ...
ѯ: 2025-08-14
ִʧ: 'gbk' codec can't encode character '\U0001f4c1' in position 0: illegal multibyte sequence
2025-08-14 15:21:49,110 - ERROR - [precise_query] 错误: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\py_mini_racer\py_mini_racer.py:13: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
Traceback (most recent call last):
  File "d:\Documents\Code\stock\Fupan\daily_limit_precisequery_auto.py", line 104, in main
    file_path = get_default_file_path()
                ^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Documents\Code\stock\Fupan\daily_limit_precisequery.py", line 832, in get_default_file_path
    print(f"\U0001f4c1 ĬExcelļ: {default_file_path}")
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4c1' in position 0: illegal multibyte sequence

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "d:\Documents\Code\stock\Fupan\daily_limit_precisequery_auto.py", line 131, in <module>
    success = main()
              ^^^^^^
  File "d:\Documents\Code\stock\Fupan\daily_limit_precisequery_auto.py", line 126, in main
    print(f"ϸ: {traceback.format_exc()}")
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4c1' in position 332: illegal multibyte sequence
2025-08-14 15:21:49,111 - ERROR - ❌ 任务 precise_query 执行失败，返回码: 1
2025-08-14 15:21:49,111 - INFO - 🔄 重试执行任务 precise_query (第1次重试)
2025-08-14 15:23:19,071 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:23:19,071 - INFO - 🛑 调度器已停止
2025-08-14 15:26:17,198 - INFO - 📅 股票数据定时任务调度器已初始化
2025-08-14 15:26:17,199 - INFO - 🚀 立即执行任务: precise_query
2025-08-14 15:26:17,199 - INFO - 🚀 开始执行任务: precise_query - 获取当日涨停精确查询数据
2025-08-14 15:26:17,199 - INFO - 执行命令: C:\Users\<USER>\.conda\envs\stock_env\python.exe d:\Documents\Code\stock\Fupan\run_precise_query.py
2025-08-14 15:26:17,199 - INFO - 工作目录: d:\Documents\Code\stock\Fupan
2025-08-14 15:26:21,510 - INFO - [precise_query] 输出: ͣȷѯ...
 
 (1/2): ...
  1 
  1 
 20250814 ...
 42 
:
: ['', '', '', '', '', '[20250814]', '[20250814]', '', '[20250814]', '[20250814]', 'a()[20250814]', '[20250814]', '[20250814]', 'a[20250814]', '[20250814]', '[20250814]', '[20250814]', '[20250814]', '[20250814]', '[20250814]', '[20250814]', '[20250814]', 'dde', '', '[20250814]', 'market_code', 'code']
...
 42 
 27 
  15 
 42  15 
:
 20250814 
...
 sheet '0814'  42 
 20250814 
 
 : 1 
  1 
ͣȷѯִгɹ
2025-08-14 15:26:21,511 - INFO - ✅ 任务 precise_query 执行成功

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel处理器
处理股票表格数据，分离股票名称和代码，整理表格格式
"""

import pandas as pd
import re
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ExcelProcessor:
    """Excel处理器"""
    
    def __init__(self):
        self.current_dir = Path(__file__).parent
        logger.info("ExcelProcessor 初始化完成")
    
    def detect_stock_name_and_code(self, text):
        """检测股票名称和6位数代码"""
        try:
            if not isinstance(text, str) or not text.strip():
                return None, None
            
            text = text.strip()
            
            # 匹配模式：股票名称 + 6位数字代码
            # 支持多种格式：
            # 1. 股票名称123456
            # 2. 股票名称 123456
            # 3. 股票名称(123456)
            # 4. 股票名称（123456）
            
            patterns = [
                r'^(.+?)\s*[\(（](\d{6})[\)）]\s*$',  # 股票名称(123456)
                r'^(.+?)\s+(\d{6})\s*$',              # 股票名称 123456
                r'^(.+?)(\d{6})\s*$',                 # 股票名称123456
            ]
            
            for pattern in patterns:
                match = re.match(pattern, text)
                if match:
                    stock_name = match.group(1).strip()
                    stock_code = match.group(2).strip()
                    
                    # 验证股票代码是否为6位数字
                    if len(stock_code) == 6 and stock_code.isdigit():
                        # 验证股票名称不为空且包含中文字符
                        if stock_name and re.search(r'[\u4e00-\u9fff]', stock_name):
                            logger.debug(f"检测到股票: {stock_name} - {stock_code}")
                            return stock_name, stock_code
            
            return None, None
            
        except Exception as e:
            logger.error(f"检测股票名称和代码失败: {e}")
            return None, None
    
    def move_single_content_to_first_column(self, df):
        """将只有一格有内容的行移动到第一列"""
        try:
            logger.info("处理单格内容行...")
            
            modified_count = 0
            
            for index, row in df.iterrows():
                # 统计非空单元格
                non_empty_cells = []
                for col_idx, value in enumerate(row):
                    if pd.notna(value) and str(value).strip():
                        non_empty_cells.append((col_idx, value))
                
                # 如果只有一个非空单元格且不在第一列
                if len(non_empty_cells) == 1:
                    col_idx, value = non_empty_cells[0]
                    if col_idx != 0:  # 不在第一列
                        # 清空原位置
                        df.iloc[index, col_idx] = ""
                        # 移动到第一列
                        df.iloc[index, 0] = value
                        modified_count += 1
                        logger.debug(f"行 {index+1}: 将内容从第 {col_idx+1} 列移动到第 1 列")
            
            logger.info(f"处理完成，共移动 {modified_count} 行的内容到第一列")
            return df
            
        except Exception as e:
            logger.error(f"移动单格内容失败: {e}")
            return df
    
    def separate_stock_name_and_code(self, df):
        """分离股票名称和代码"""
        try:
            logger.info("开始分离股票名称和代码...")
            
            # 确保至少有两列
            if len(df.columns) < 2:
                df[1] = ""  # 添加第二列
            
            modified_count = 0
            
            for index, row in df.iterrows():
                first_cell = row.iloc[0]
                
                if pd.notna(first_cell):
                    stock_name, stock_code = self.detect_stock_name_and_code(str(first_cell))
                    
                    if stock_name and stock_code:
                        # 更新第一列为股票名称
                        df.iloc[index, 0] = stock_name
                        # 将股票代码放到第二列
                        df.iloc[index, 1] = stock_code
                        modified_count += 1
                        logger.debug(f"行 {index+1}: {first_cell} -> {stock_name} | {stock_code}")
            
            logger.info(f"分离完成，共处理 {modified_count} 行股票数据")
            return df
            
        except Exception as e:
            logger.error(f"分离股票名称和代码失败: {e}")
            return df


    def process_excel_file(self, input_path, output_path=None):
        """处理Excel文件"""
        try:
            logger.info("="*60)
            logger.info(f"开始处理Excel文件: {input_path}")
            logger.info("="*60)
            
            # 读取Excel文件
            input_file = Path(input_path)
            if not input_file.exists():
                logger.error(f"输入文件不存在: {input_path}")
                return False
            
            # 读取数据
            df = pd.read_excel(input_file, header=None)
            logger.info(f"读取Excel文件: {len(df)} 行 x {len(df.columns)} 列")
            
            # 处理单格内容行
            df = self.move_single_content_to_first_column(df)
            
            # 分离股票名称和代码
            df = self.separate_stock_name_and_code(df)
            
            # 确定输出路径
            if output_path is None:
                output_path = input_file.parent / f"{input_file.stem}_processed.xlsx"
            
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存处理后的文件
            with pd.ExcelWriter(str(output_file), engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='处理后数据', index=False, header=False)
                
                # 获取工作表并设置格式
                worksheet = writer.sheets['处理后数据']
                
                # 自动调整列宽
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            logger.info(f"✅ 处理完成，输出文件: {output_file}")
            logger.info(f"📊 最终数据规模: {len(df)} 行 x {len(df.columns)} 列")
            logger.info("="*60)

            return str(output_file)

        except Exception as e:
            logger.error(f"处理Excel文件失败: {e}")
            return None
    
    def batch_process_excel_files(self, input_dir, output_dir=None):
        """批量处理Excel文件"""
        try:
            input_path = Path(input_dir)
            if not input_path.exists():
                logger.error(f"输入目录不存在: {input_dir}")
                return []
            
            # 查找所有Excel文件
            excel_files = list(input_path.glob("*.xlsx")) + list(input_path.glob("*.xls"))
            
            if not excel_files:
                logger.warning(f"在 {input_dir} 中未找到Excel文件")
                return []
            
            logger.info(f"找到 {len(excel_files)} 个Excel文件")
            
            processed_files = []
            
            for excel_file in excel_files:
                logger.info(f"处理文件: {excel_file.name}")
                
                if output_dir:
                    output_path = Path(output_dir) / f"{excel_file.stem}_processed.xlsx"
                else:
                    output_path = None
                
                success = self.process_excel_file(excel_file, output_path)
                
                if success:
                    processed_files.append(str(output_path) if output_path else str(excel_file.parent / f"{excel_file.stem}_processed.xlsx"))
            
            logger.info(f"批量处理完成，成功处理 {len(processed_files)} 个文件")
            return processed_files
            
        except Exception as e:
            logger.error(f"批量处理失败: {e}")
            return []

def main():
    """主函数"""
    try:
        processor = ExcelProcessor()
        
        # 处理output目录下的所有Excel文件
        current_dir = Path(__file__).parent
        input_dir = current_dir / "output"
        
        if not input_dir.exists():
            logger.error(f"输出目录不存在: {input_dir}")
            logger.info("请先运行表格处理程序生成Excel文件")
            return
        
        # 批量处理
        processed_files = processor.batch_process_excel_files(input_dir)
        
        if processed_files:
            logger.info("✅ 批量处理成功！")
            for file_path in processed_files:
                logger.info(f"  📄 {file_path}")
        else:
            logger.error("❌ 批量处理失败")
        
    except Exception as e:
        logger.error(f"主程序执行失败: {e}")

if __name__ == "__main__":
    main()

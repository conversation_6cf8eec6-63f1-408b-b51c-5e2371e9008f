#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自适应表格处理器
根据图像尺寸自动计算切分块数，精准切分后使用有线表格识别，最终输出Excel
"""

import cv2
import numpy as np
from pathlib import Path
import logging
import json
import time
import pandas as pd
from bs4 import BeautifulSoup
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AdaptiveTableProcessor:
    """自适应表格处理器"""
    
    def __init__(self):
        self.max_dimension = 1500      # 最大处理尺寸
        self.min_scale_factor = 0.9    # 最小缩放比例
        self.current_dir = Path(__file__).parent
        
        # 初始化标志
        self.engines_initialized = False
        self.table_cls = None
        self.wired_engine = None
        self.ocr_engine = None
        
        logger.info("AdaptiveTableProcessor 初始化完成")
        logger.info(f"最大处理尺寸: {self.max_dimension}px")
        logger.info(f"最小缩放比例: {self.min_scale_factor}")
    
    def calculate_optimal_segments(self, image_height, image_width):
        """根据图像尺寸自动计算最优切分块数"""
        try:
            # 根据缩放比例限制计算最大允许的片段尺寸
            max_segment_height = int(self.max_dimension / self.min_scale_factor)
            max_segment_width = int(self.max_dimension / self.min_scale_factor)
            
            # 计算需要的切分块数
            height_segments = max(1, int(np.ceil(image_height / max_segment_height)))
            width_segments = max(1, int(np.ceil(image_width / max_segment_width)))
            
            # 由于我们主要按高度切分，使用高度切分数
            optimal_segments = height_segments
            
            # 计算实际的片段高度
            actual_segment_height = image_height / optimal_segments
            actual_scale_factor = self.max_dimension / max(actual_segment_height, image_width)
            
            logger.info(f"图像尺寸: {image_width} x {image_height}")
            logger.info(f"最大允许片段高度: {max_segment_height} (缩放比例≥{self.min_scale_factor})")
            logger.info(f"计算得出最优切分块数: {optimal_segments}")
            logger.info(f"实际片段高度: {actual_segment_height:.0f}")
            logger.info(f"预期缩放比例: {actual_scale_factor:.3f}")
            
            return optimal_segments, int(actual_segment_height)
            
        except Exception as e:
            logger.error(f"计算最优切分块数失败: {e}")
            return 1, image_height
    
    def initialize_engines(self):
        """初始化TableStructureRec引擎"""
        if self.engines_initialized:
            return
        
        try:
            logger.info("正在初始化TableStructureRec引擎...")
            
            from table_cls import TableCls
            from wired_table_rec.main import WiredTableInput, WiredTableRecognition
            from rapidocr import RapidOCR
            
            # 初始化组件
            self.table_cls = TableCls()
            
            # 初始化有线表格识别引擎
            wired_input = WiredTableInput(model_type="unet")
            self.wired_engine = WiredTableRecognition(wired_input)
            
            # 初始化OCR引擎
            self.ocr_engine = RapidOCR()
            
            self.engines_initialized = True
            logger.info("✅ TableStructureRec引擎初始化成功")
            
        except ImportError as e:
            logger.error(f"❌ 缺少必要的库: {e}")
            raise
        except Exception as e:
            logger.error(f"❌ 引擎初始化失败: {e}")
            raise
    
    def detect_thick_horizontal_lines(self, image):
        """检测粗横线"""
        try:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 多种方法检测粗横线
            horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (100, 3))
            horizontal_lines = cv2.morphologyEx(gray, cv2.MORPH_OPEN, horizontal_kernel)
            
            edges = cv2.Canny(gray, 30, 100, apertureSize=3)
            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50, minLineLength=200, maxLineGap=10)
            
            height, width = gray.shape
            line_mask = np.zeros_like(gray)
            line_mask = cv2.bitwise_or(line_mask, horizontal_lines)
            
            if lines is not None:
                for line in lines:
                    x1, y1, x2, y2 = line[0]
                    if abs(y2 - y1) <= abs(x2 - x1) * 0.2:
                        cv2.line(line_mask, (x1, y1), (x2, y2), 255, 3)
            
            kernel = np.ones((1, 5), np.uint8)
            line_mask = cv2.morphologyEx(line_mask, cv2.MORPH_CLOSE, kernel)
            
            return line_mask
            
        except Exception as e:
            logger.error(f"检测粗横线失败: {e}")
            return None
    
    def find_precise_split_lines(self, image, line_mask, target_segments, target_height):
        """找到精准的切分线位置"""
        try:
            height, width = image.shape[:2]
            
            # 分析每行的线条强度
            row_line_strength = []
            for y in range(height):
                line_pixels = np.sum(line_mask[y, :] > 0)
                strength = line_pixels / width
                row_line_strength.append(strength)
            
            # 找到线条强度较高的行
            threshold = np.mean(row_line_strength) + 1.5 * np.std(row_line_strength)
            strong_line_rows = [(y, strength) for y, strength in enumerate(row_line_strength) if strength > threshold]
            
            logger.info(f"检测到 {len(strong_line_rows)} 个强线条候选位置")
            
            if not strong_line_rows:
                logger.warning("未检测到明显的粗横线，使用均匀分割")
                return self.uniform_split_positions(height, target_segments)
            
            # 合并相近的线条
            merged_lines = self.merge_nearby_lines(strong_line_rows)
            logger.info(f"合并后剩余 {len(merged_lines)} 个线条位置")
            
            # 选择最佳的切分位置
            split_positions = self.select_optimal_split_positions(merged_lines, height, target_segments, target_height)
            
            return split_positions
            
        except Exception as e:
            logger.error(f"查找精准切分线失败: {e}")
            return self.uniform_split_positions(height, target_segments)
    
    def merge_nearby_lines(self, line_rows, merge_distance=15):
        """合并相近的线条"""
        if not line_rows:
            return []
        
        line_rows.sort(key=lambda x: x[0])
        merged = []
        current_group = [line_rows[0]]
        
        for i in range(1, len(line_rows)):
            current_y, current_strength = line_rows[i]
            last_y, last_strength = current_group[-1]
            
            if current_y - last_y <= merge_distance:
                current_group.append(line_rows[i])
            else:
                best_line = max(current_group, key=lambda x: x[1])
                merged.append(best_line[0])
                current_group = [line_rows[i]]
        
        if current_group:
            best_line = max(current_group, key=lambda x: x[1])
            merged.append(best_line[0])
        
        return merged
    
    def select_optimal_split_positions(self, line_positions, total_height, target_segments, target_height):
        """选择最优的切分位置"""
        if not line_positions:
            return self.uniform_split_positions(total_height, target_segments)
        
        # 计算理想的切分位置
        ideal_positions = []
        for i in range(1, target_segments):
            ideal_pos = i * target_height
            if ideal_pos < total_height:
                ideal_positions.append(ideal_pos)
        
        logger.info(f"理想切分位置: {ideal_positions}")
        logger.info(f"检测到的线条位置: {line_positions}")
        
        # 为每个理想位置找到最近的线条
        selected_positions = []
        used_lines = set()
        
        for ideal_pos in ideal_positions:
            best_line = None
            min_distance = float('inf')
            
            for line_pos in line_positions:
                if line_pos not in used_lines:
                    distance = abs(line_pos - ideal_pos)
                    if distance < min_distance:
                        min_distance = distance
                        best_line = line_pos
            
            if best_line is not None and min_distance < target_height * 0.3:
                selected_positions.append(best_line)
                used_lines.add(best_line)
                logger.info(f"理想位置 {ideal_pos} -> 实际线条位置 {best_line} (偏差: {min_distance})")
            else:
                selected_positions.append(ideal_pos)
                logger.info(f"理想位置 {ideal_pos} -> 使用理想位置 (无合适线条)")
        
        return sorted(selected_positions)
    
    def uniform_split_positions(self, height, target_segments):
        """均匀切分位置"""
        positions = []
        segment_height = height // target_segments
        
        for i in range(1, target_segments):
            pos = i * segment_height
            if pos < height:
                positions.append(pos)
        
        return positions
    
    def split_image_adaptively(self, image_path):
        """自适应切分图像"""
        try:
            logger.info(f"开始自适应图像切分: {image_path}")
            
            # 读取图像
            image = cv2.imread(str(image_path))
            if image is None:
                logger.error(f"无法读取图像: {image_path}")
                return []
            
            height, width = image.shape[:2]
            logger.info(f"原始图像尺寸: {width} x {height}")
            
            # 计算最优切分块数
            target_segments, target_height = self.calculate_optimal_segments(height, width)
            
            # 检测粗横线
            line_mask = self.detect_thick_horizontal_lines(image)
            if line_mask is None:
                logger.warning("线条检测失败，使用均匀切分")
                split_positions = self.uniform_split_positions(height, target_segments)
            else:
                split_positions = self.find_precise_split_lines(image, line_mask, target_segments, target_height)
            
            # 执行切分
            segments = []
            last_y = 0
            
            for i, split_y in enumerate(split_positions):
                segment = image[last_y:split_y, :]
                segments.append(segment)
                
                segment_height = split_y - last_y
                scale_factor = self.max_dimension / max(segment.shape[:2])
                
                logger.info(f"片段 {len(segments)}: {last_y}-{split_y} (高度: {segment_height}, 缩放: {scale_factor:.3f})")
                last_y = split_y
            
            # 处理最后一个片段
            if height - last_y > 100:
                final_segment = image[last_y:height, :]
                segments.append(final_segment)
                
                segment_height = height - last_y
                scale_factor = self.max_dimension / max(final_segment.shape[:2])
                
                logger.info(f"最后片段: {last_y}-{height} (高度: {segment_height}, 缩放: {scale_factor:.3f})")
            
            logger.info(f"总共切分出 {len(segments)} 个片段")
            
            # 验证缩放比例
            for i, segment in enumerate(segments):
                max_dim = max(segment.shape[:2])
                scale_factor = self.max_dimension / max_dim
                if scale_factor < self.min_scale_factor:
                    logger.warning(f"片段 {i+1} 缩放比例 {scale_factor:.3f} 小于最小要求 {self.min_scale_factor}")
                else:
                    logger.info(f"片段 {i+1} 缩放比例 {scale_factor:.3f} ✅")
            
            return segments
            
        except Exception as e:
            logger.error(f"自适应切分失败: {e}")
            return []
    
    def save_segments(self, segments, output_dir):
        """保存切分后的片段"""
        try:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            saved_files = []
            
            for i, segment in enumerate(segments):
                filename = f"adaptive_segment_{i+1:02d}.png"
                file_path = output_path / filename
                
                cv2.imwrite(str(file_path), segment)
                saved_files.append(str(file_path))
                
                height, width = segment.shape[:2]
                scale_factor = self.max_dimension / max(height, width)
                
                logger.info(f"保存片段 {i+1}: {filename} ({width}x{height}, 缩放: {scale_factor:.3f})")
            
            return saved_files
            
        except Exception as e:
            logger.error(f"保存片段失败: {e}")
            return []

    def preprocess_image(self, image_path):
        """图像预处理 - 分辨率优化"""
        try:
            if isinstance(image_path, (str, Path)):
                image = cv2.imread(str(image_path))
                if image is None:
                    raise ValueError(f"无法读取图像: {image_path}")
            else:
                image = image_path

            original_height, original_width = image.shape[:2]
            max_dimension = max(original_height, original_width)

            if max_dimension > self.max_dimension:
                scale = self.max_dimension / max_dimension
                new_width = int(original_width * scale)
                new_height = int(original_height * scale)

                resized_image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
                logger.info(f"图像已缩放至: {new_width} x {new_height} (缩放比例: {scale:.3f})")

                return resized_image, scale
            else:
                logger.info("图像尺寸在限制范围内，无需缩放")
                return image, 1.0

        except Exception as e:
            logger.error(f"图像预处理失败: {e}")
            raise

    def perform_ocr(self, image):
        """执行OCR识别"""
        try:
            start_time = time.time()

            rapid_ocr_output = self.ocr_engine(image, return_word_box=True)

            if rapid_ocr_output is None or not hasattr(rapid_ocr_output, 'boxes'):
                logger.warning("OCR识别结果为空")
                return []

            ocr_result = list(zip(
                rapid_ocr_output.boxes,
                rapid_ocr_output.txts,
                rapid_ocr_output.scores
            ))

            elapsed = time.time() - start_time
            logger.info(f"OCR识别完成: 检测到 {len(ocr_result)} 个文本框 (耗时: {elapsed:.3f}s)")

            return ocr_result

        except Exception as e:
            logger.error(f"OCR识别失败: {e}")
            return []

    def recognize_table_structure(self, image, ocr_result):
        """表格结构识别 - 强制使用有线表格识别"""
        try:
            start_time = time.time()

            logger.info("使用有线表格识别引擎 (wired_table_rec_v2)")

            table_results = self.wired_engine(
                image,
                ocr_result=ocr_result,
                enhance_box_line=True,
                rotated_fix=True,
                need_ocr=True,
            )

            elapsed = time.time() - start_time
            cell_count = len(table_results.cell_bboxes) if table_results.cell_bboxes is not None else 0

            logger.info(f"表格结构识别完成: 检测到 {cell_count} 个单元格 (耗时: {elapsed:.3f}s)")

            return table_results

        except Exception as e:
            logger.error(f"表格结构识别失败: {e}")
            return None

    def process_single_segment(self, segment_image, segment_index):
        """处理单个片段"""
        try:
            logger.info(f"🔄 处理片段 {segment_index}")

            # 确保引擎已初始化
            self.initialize_engines()

            # 图像预处理
            processed_image, scale_factor = self.preprocess_image(segment_image)

            # OCR识别
            ocr_result = self.perform_ocr(processed_image)

            # 表格结构识别
            table_results = self.recognize_table_structure(processed_image, ocr_result)

            if table_results is None:
                logger.error(f"片段 {segment_index} 表格结构识别失败")
                return None

            return {
                "segment_index": segment_index,
                "scale_factor": scale_factor,
                "cell_count": len(table_results.cell_bboxes) if table_results.cell_bboxes is not None else 0,
                "ocr_text_count": len(ocr_result),
                "processing_time": table_results.elapse,
                "html_content": table_results.pred_html,
                "table_results": table_results
            }

        except Exception as e:
            logger.error(f"处理片段 {segment_index} 失败: {e}")
            return None

    def html_to_dataframe(self, html_content):
        """将HTML表格转换为DataFrame"""
        try:
            if not html_content or html_content.strip() == "":
                logger.warning("HTML内容为空")
                return pd.DataFrame()

            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(html_content, 'html.parser')
            table = soup.find('table')

            if table is None:
                logger.warning("HTML中未找到表格")
                return pd.DataFrame()

            # 提取表格数据
            rows = []
            for tr in table.find_all('tr'):
                row = []
                for cell in tr.find_all(['td', 'th']):
                    # 获取单元格文本，处理换行符
                    cell_text = cell.get_text(strip=True)
                    cell_text = re.sub(r'\s+', ' ', cell_text)  # 合并多个空白字符
                    row.append(cell_text)
                if row:  # 只添加非空行
                    rows.append(row)

            if not rows:
                logger.warning("表格中没有数据行")
                return pd.DataFrame()

            # 确保所有行的列数一致
            max_cols = max(len(row) for row in rows) if rows else 0
            for row in rows:
                while len(row) < max_cols:
                    row.append("")

            # 创建DataFrame
            df = pd.DataFrame(rows)

            logger.info(f"成功转换HTML表格: {len(df)} 行 x {len(df.columns)} 列")
            return df

        except Exception as e:
            logger.error(f"HTML转DataFrame失败: {e}")
            return pd.DataFrame()

    def merge_dataframes(self, dataframes):
        """合并多个DataFrame"""
        try:
            if not dataframes:
                logger.warning("没有DataFrame需要合并")
                return pd.DataFrame()

            # 过滤空的DataFrame
            valid_dfs = [df for df in dataframes if not df.empty]

            if not valid_dfs:
                logger.warning("所有DataFrame都为空")
                return pd.DataFrame()

            # 确保所有DataFrame的列数一致
            max_cols = max(len(df.columns) for df in valid_dfs)

            for df in valid_dfs:
                while len(df.columns) < max_cols:
                    df[len(df.columns)] = ""

            # 合并DataFrame
            merged_df = pd.concat(valid_dfs, ignore_index=True)

            logger.info(f"成功合并 {len(valid_dfs)} 个表格: 总计 {len(merged_df)} 行 x {len(merged_df.columns)} 列")
            return merged_df

        except Exception as e:
            logger.error(f"合并DataFrame失败: {e}")
            return pd.DataFrame()

    def save_to_excel(self, dataframe, output_path):
        """保存DataFrame到Excel"""
        try:
            if dataframe.empty:
                logger.warning("DataFrame为空，无法保存到Excel")
                return False

            # 创建输出目录
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)

            # 保存到Excel
            with pd.ExcelWriter(str(output_file), engine='openpyxl') as writer:
                dataframe.to_excel(writer, sheet_name='表格数据', index=False, header=False)

                # 获取工作表并设置格式
                worksheet = writer.sheets['表格数据']

                # 自动调整列宽
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter

                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass

                    adjusted_width = min(max_length + 2, 50)  # 限制最大宽度
                    worksheet.column_dimensions[column_letter].width = adjusted_width

            logger.info(f"✅ Excel文件已保存: {output_file}")
            logger.info(f"📊 数据规模: {len(dataframe)} 行 x {len(dataframe.columns)} 列")

            return True

        except Exception as e:
            logger.error(f"保存Excel失败: {e}")
            return False

    def process_complete_workflow(self, image_path, output_excel_path):
        """完整的工作流程：切分 -> 识别 -> 合并 -> 输出Excel"""
        try:
            logger.info("="*60)
            logger.info("🚀 开始自适应表格处理完整工作流程")
            logger.info("="*60)

            start_time = time.time()

            # 第1步：自适应切分图像
            logger.info("📋 第1步：自适应切分图像")
            segments = self.split_image_adaptively(image_path)

            if not segments:
                logger.error("❌ 图像切分失败")
                return False

            # 第2步：处理每个片段
            logger.info(f"📋 第2步：处理 {len(segments)} 个片段")
            segment_results = []
            dataframes = []

            for i, segment in enumerate(segments, 1):
                result = self.process_single_segment(segment, i)
                if result:
                    segment_results.append(result)

                    # 转换HTML为DataFrame
                    df = self.html_to_dataframe(result["html_content"])
                    if not df.empty:
                        dataframes.append(df)
                        logger.info(f"✅ 片段 {i} 处理成功: {result['cell_count']} 个单元格")
                    else:
                        logger.warning(f"⚠️ 片段 {i} 转换DataFrame失败")
                else:
                    logger.error(f"❌ 片段 {i} 处理失败")

            if not dataframes:
                logger.error("❌ 没有成功处理的片段")
                return False

            # 第3步：合并所有DataFrame
            logger.info("📋 第3步：合并表格数据")
            merged_df = self.merge_dataframes(dataframes)

            if merged_df.empty:
                logger.error("❌ 合并DataFrame失败")
                return False

            # 第4步：保存到Excel
            logger.info("📋 第4步：保存到Excel")
            success = self.save_to_excel(merged_df, output_excel_path)

            if not success:
                logger.error("❌ 保存Excel失败")
                return False

            # 统计信息
            total_time = time.time() - start_time
            total_cells = sum(r["cell_count"] for r in segment_results)
            total_ocr_texts = sum(r["ocr_text_count"] for r in segment_results)

            logger.info("="*60)
            logger.info("🎉 自适应表格处理完成！")
            logger.info(f"📊 处理统计:")
            logger.info(f"   切分片段数: {len(segments)}")
            logger.info(f"   成功处理片段: {len(segment_results)}")
            logger.info(f"   总单元格数: {total_cells}")
            logger.info(f"   总OCR文本框: {total_ocr_texts}")
            logger.info(f"   最终表格规模: {len(merged_df)} 行 x {len(merged_df.columns)} 列")
            logger.info(f"   总处理时间: {total_time:.3f}秒")
            logger.info(f"   Excel文件: {output_excel_path}")
            logger.info("="*60)

            return True

        except Exception as e:
            logger.error(f"完整工作流程失败: {e}")
            return False



def main():
    """主函数"""
    try:
        processor = AdaptiveTableProcessor()

        # 查找images目录中的图片文件
        images_dir = processor.current_dir / "images"
        if not images_dir.exists():
            logger.error(f"图像目录不存在: {images_dir}")
            return

        # 查找第一个图片文件
        image_files = list(images_dir.glob("*.png")) + list(images_dir.glob("*.jpg")) + list(images_dir.glob("*.jpeg"))

        if not image_files:
            logger.error(f"在 {images_dir} 目录中未找到图片文件")
            return

        image_path = image_files[0]
        logger.info(f"使用图片文件: {image_path}")

        # 输出Excel路径
        output_excel = processor.current_dir / "output" / f"{image_path.stem}_processed.xlsx"

        # 执行完整工作流程
        success = processor.process_complete_workflow(image_path, output_excel)

        if success:
            logger.info("🎉 处理成功！")
        else:
            logger.error("❌ 处理失败")

    except Exception as e:
        logger.error(f"主程序执行失败: {e}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网页爬取器
从指定网站获取涨停复盘相关网页，下载主图并按日期命名
"""

import requests
from bs4 import BeautifulSoup
import re
from pathlib import Path
import logging
from urllib.parse import urljoin, urlparse
import time
from PIL import Image
import io

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WebScraper:
    """网页爬取器"""
    
    def __init__(self):
        self.base_url = "https://yuanchuang.10jqka.com.cn/mrnxgg_list/"
        self.current_dir = Path(__file__).parent
        self.image_dir = self.current_dir / "images"
        self.image_dir.mkdir(exist_ok=True)
        
        # 设置请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        logger.info("WebScraper 初始化完成")
    
    def get_page_content(self, url):
        """获取网页内容"""
        try:
            response = requests.get(url, headers=self.headers, timeout=10)
            response.raise_for_status()

            # 尝试自动检测编码
            if response.encoding == 'ISO-8859-1':
                # 如果检测到的编码是ISO-8859-1，尝试其他编码
                encodings = ['utf-8', 'gbk', 'gb2312']
                for encoding in encodings:
                    try:
                        response.encoding = encoding
                        content = response.text
                        # 简单验证：检查是否包含中文字符
                        if '涨停' in content or '复盘' in content:
                            return content
                    except:
                        continue
            else:
                response.encoding = response.apparent_encoding or 'utf-8'

            return response.text
        except Exception as e:
            logger.error(f"获取网页内容失败: {e}")
            return None
    
    def find_ztfp_articles(self):
        """查找涨停复盘相关文章"""
        try:
            content = self.get_page_content(self.base_url)
            if not content:
                return []

            # 设置正确的编码
            soup = BeautifulSoup(content, 'html.parser')
            articles = []

            all_links = soup.find_all('a', href=True)

            for link in all_links:
                href = link['href']
                title = link.get_text(strip=True)

                # 检查是否是文章链接格式：/YYYYMMDD/cXXXXXXXXX.shtml
                if re.match(r'/\d{8}/c\d+\.shtml', href):
                    # 构建完整URL
                    full_url = urljoin(self.base_url, href)

                    # 检查标题是否包含涨停复盘关键词
                    if "涨停复盘" in title and len(title) > 5:
                        # 避免重复添加
                        if not any(article['url'] == full_url for article in articles):
                            articles.append({
                                'title': title,
                                'url': full_url
                            })
                            logger.info(f"找到涨停相关文章: {title}")

                    # 如果是今日的涨停复盘文章，优先添加
                    elif "涨停复盘" in title and "2025" in href:
                        if not any(article['url'] == full_url for article in articles):
                            articles.append({
                                'title': title,
                                'url': full_url
                            })
                            logger.info(f"找到今日涨停复盘文章: {title}")

            # 如果仍然没有找到文章，查找所有包含涨停复盘关键词的链接
            if not articles:
                logger.debug("未找到标准格式文章，搜索所有涨停复盘链接...")
                for link in all_links:
                    title = link.get_text(strip=True)
                    href = link['href']

                    if "涨停复盘" in title and len(title) > 5:
                        # 处理相对链接和绝对链接
                        if href.startswith('http'):
                            full_url = href
                        else:
                            full_url = urljoin(self.base_url, href)

                        if not any(article['url'] == full_url for article in articles):
                            articles.append({
                                'title': title,
                                'url': full_url
                            })
                            logger.debug(f"找到涨停复盘文章: {title}")

            logger.info(f"✅ 找到 {len(articles)} 篇涨停复盘文章")
            return articles

        except Exception as e:
            logger.error(f"查找涨停复盘文章失败: {e}")
            return []
    
    def extract_date_from_content(self, content):
        """从网页内容中提取日期"""
        try:
            date_pattern = r'(\d{4})\s*-\s*(\d{2})\s*-\s*(\d{2})\s+\d{2}:\d{2}:\d{2}'
            match = re.search(date_pattern, content)
            
            if match:
                year, month, day = match.groups()
                date_str = f"{year}-{month}-{day}"
                logger.info(f"提取到日期: {date_str}")
                return date_str
            
            # 备用匹配模式
            date_pattern2 = r'(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*日'
            match2 = re.search(date_pattern2, content)
            
            if match2:
                year, month, day = match2.groups()
                date_str = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                logger.info(f"提取到日期(备用格式): {date_str}")
                return date_str
            
            logger.warning("未找到日期信息")
            return None
            
        except Exception as e:
            logger.error(f"提取日期失败: {e}")
            return None
    
    def get_image_size(self, image_url):
        """获取图片尺寸"""
        try:
            # 跳过base64编码的图片（data URLs）
            if image_url.startswith('data:'):
                logger.info(f"跳过base64编码图片: {image_url[:50]}...")
                return 0, None

            # 跳过小图标和装饰图片
            if any(keyword in image_url.lower() for keyword in ['icon', 'logo', 'avatar', 'thumb', 'banner']):
                logger.info(f"跳过装饰图片: {image_url}")
                return 0, None

            response = requests.get(image_url, headers=self.headers, timeout=10)
            response.raise_for_status()

            # 检查内容类型
            content_type = response.headers.get('content-type', '').lower()
            if not content_type.startswith('image/'):
                logger.info(f"跳过非图片内容: {image_url} (类型: {content_type})")
                return 0, None

            image = Image.open(io.BytesIO(response.content))
            width, height = image.size
            file_size = len(response.content)

            # 过滤太小的图片
            if width < 200 or height < 200:
                logger.info(f"跳过小尺寸图片: {image_url} ({width}x{height})")
                return 0, None

            # 返回文件大小而不是像素大小，更准确
            return file_size, response.content

        except Exception as e:
            logger.error(f"获取图片尺寸失败: {e}")
            return 0, None
    
    def find_main_images(self, article_url):
        """查找文章中的主要图片（多张较大图片）"""
        try:
            content = self.get_page_content(article_url)
            if not content:
                return [], None

            soup = BeautifulSoup(content, 'html.parser')

            # 提取日期
            date_str = self.extract_date_from_content(content)

            # 查找所有图片
            images = soup.find_all('img', src=True)

            if not images:
                logger.warning("未找到图片")
                return [], date_str

            logger.info(f"找到 {len(images)} 张图片，正在分析...")

            # 收集图片信息，保持网页中的出现顺序
            image_info = []

            for order_index, img in enumerate(images):
                img_src = img['src']

                # 处理相对链接
                if img_src.startswith('//'):
                    img_url = 'https:' + img_src
                elif img_src.startswith('/'):
                    img_url = urljoin(article_url, img_src)
                elif img_src.startswith('data:'):
                    # 跳过base64编码的图片
                    continue
                elif not img_src.startswith('http'):
                    img_url = urljoin(article_url, img_src)
                else:
                    img_url = img_src

                # 跳过小图标和装饰图片（在get_image_size中也会检查）
                if any(keyword in img_url.lower() for keyword in ['icon', 'logo', 'avatar', 'thumb', 'banner']):
                    continue

                size, content = self.get_image_size(img_url)

                # 只有成功获取到图片内容才记录
                if size > 0 and content is not None:
                    logger.info(f"图片: {img_url[:80]}... - 尺寸: {size}")
                    image_info.append({
                        'url': img_url,
                        'size': size,
                        'content': content,
                        'order': order_index  # 保存在网页中的出现顺序
                    })

                time.sleep(0.3)  # 避免请求过快

            # 过滤出较大的图片（大于100KB），然后按网页出现顺序排序
            large_images = [img for img in image_info if img['size'] > 100000]
            large_images.sort(key=lambda x: x['order'])  # 按网页出现顺序排序

            # 返回前5张较大的图片，按网页顺序
            selected_images = large_images[:5]

            if selected_images:
                logger.info(f"选择了 {len(selected_images)} 张主要图片")
                for i, img in enumerate(selected_images, 1):
                    logger.info(f"主图{i}: {img['url']} - 尺寸: {img['size']}")
                return selected_images, date_str
            else:
                logger.warning("未找到合适的主图")
                return [], date_str

        except Exception as e:
            logger.error(f"查找主图失败: {e}")
            return [], None
    
    def save_images(self, images_data, date_str):
        """保存多张图片"""
        try:
            if not images_data or not date_str:
                logger.error("图片数据或日期为空")
                return []

            saved_paths = []

            for i, image_info in enumerate(images_data, 1):
                # 生成文件名
                filename = f"{date_str}-{i}.png"
                file_path = self.image_dir / filename

                # 保存图片
                with open(file_path, 'wb') as f:
                    f.write(image_info['content'])

                logger.info(f"图片已保存: {file_path}")
                saved_paths.append(str(file_path))

            return saved_paths

        except Exception as e:
            logger.error(f"保存图片失败: {e}")
            return []
    
    def scrape_all_articles(self):
        """爬取所有涨停复盘文章的主图"""
        try:
            logger.info("开始爬取涨停复盘文章...")

            articles = self.find_ztfp_articles()

            # 如果没有找到文章，直接报错
            if not articles:
                logger.error("未找到涨停复盘文章")
                return []

            saved_images = []

            for i, article in enumerate(articles, 1):
                logger.info(f"处理第 {i}/{len(articles)} 篇文章: {article['title']}")

                images_data, date_str = self.find_main_images(article['url'])

                if images_data and date_str:
                    saved_paths = self.save_images(images_data, date_str)
                    saved_images.extend(saved_paths)
                else:
                    # 如果没有找到日期，从URL中提取日期
                    if images_data and not date_str:
                        # 尝试从URL中提取日期
                        url_date_match = re.search(r'/(\d{4})(\d{2})(\d{2})/', article['url'])
                        if url_date_match:
                            year, month, day = url_date_match.groups()
                            date_str = f"{year}-{month}-{day}"
                        else:
                            from datetime import datetime
                            date_str = datetime.now().strftime("%Y-%m-%d")

                        saved_paths = self.save_images(images_data, date_str)
                        saved_images.extend(saved_paths)

                time.sleep(1)  # 避免请求过快

            logger.info(f"爬取完成，共保存 {len(saved_images)} 张图片")
            return saved_images

        except Exception as e:
            logger.error(f"爬取文章失败: {e}")
            return []

def main():
    """主函数"""
    try:
        scraper = WebScraper()
        saved_images = scraper.scrape_all_articles()
        
        if saved_images:
            logger.info("✅ 爬取成功！")
            for img_path in saved_images:
                logger.info(f"  📄 {img_path}")
        else:
            logger.error("❌ 爬取失败")
        
    except Exception as e:
        logger.error(f"主程序执行失败: {e}")

if __name__ == "__main__":
    main()

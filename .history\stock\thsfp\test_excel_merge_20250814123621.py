#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Excel合并问题 - 修复版本
"""

import pandas as pd
from pathlib import Path
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main_controller import MainController

def test_fixed_merge():
    """测试修复后的合并逻辑"""

    print("="*60)
    print("🧪 测试修复后的Excel合并")
    print("="*60)

    # 检查临时Excel文件
    output_dir = Path("output")
    temp_files = [
        output_dir / "2025-08-13-1_temp.xlsx",
        output_dir / "2025-08-13-2_temp.xlsx"
    ]

    print("\n📋 检查临时Excel文件:")

    dfs = []
    for i, temp_file in enumerate(temp_files, 1):
        if temp_file.exists():
            try:
                df = pd.read_excel(temp_file)
                dfs.append(df)

                print(f"\n📄 文件 {i}: {temp_file.name}")
                print(f"   行数: {len(df)}")
                print(f"   列数: {len(df.columns)}")
                print(f"   前3行第1列数据: {[str(df.iloc[j, 0])[:30] for j in range(min(3, len(df)))]}")

            except Exception as e:
                print(f"   ❌ 读取失败: {e}")
        else:
            print(f"   ❌ 文件不存在: {temp_file}")

    if len(dfs) == 2:
        print(f"\n🧪 测试修复后的合并逻辑:")

        # 使用修复后的合并逻辑
        controller = MainController()
        test_output = output_dir / "test_fixed_merge.xlsx"

        success = controller.merge_excel_files(temp_files, test_output)

        if success and test_output.exists():
            try:
                df_merged = pd.read_excel(test_output)

                print(f"\n📊 修复后合并结果:")
                print(f"   行数: {len(df_merged)}")
                print(f"   列数: {len(df_merged.columns)}")
                print(f"   预期行数: {len(dfs[0]) + len(dfs[1])}")

                # 检查数据分布
                df1_rows = len(dfs[0])
                df2_rows = len(dfs[1])

                print(f"\n🔍 数据分布验证:")
                print(f"   文件1行数: {df1_rows}")
                print(f"   文件2行数: {df2_rows}")
                print(f"   合并后总行数: {len(df_merged)}")

                if len(df_merged) == df1_rows + df2_rows:
                    print("   ✅ 行数匹配")
                else:
                    print("   ❌ 行数不匹配")

                # 检查分界处数据
                print(f"\n🔍 分界处数据验证:")
                boundary_idx = df1_rows - 1

                if boundary_idx < len(df_merged):
                    print(f"   文件1最后一行 (行{boundary_idx+1}): {str(df_merged.iloc[boundary_idx, 0])[:40]}")

                if boundary_idx + 1 < len(df_merged):
                    print(f"   文件2第一行 (行{boundary_idx+2}): {str(df_merged.iloc[boundary_idx+1, 0])[:40]}")

                # 验证列对齐
                print(f"\n🔍 列对齐验证:")
                print(f"   合并后列数: {len(df_merged.columns)}")
                print(f"   应该是7列: {'✅' if len(df_merged.columns) == 7 else '❌'}")

                print(f"\n✅ 修复后的合并文件已保存: {test_output}")

            except Exception as e:
                print(f"   ❌ 读取修复后合并文件失败: {e}")
        else:
            print("   ❌ 修复后合并失败")

    print("\n" + "="*60)
    print("🎉 测试完成")
    print("="*60)

if __name__ == "__main__":
    test_fixed_merge()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主控制程序
整合网页爬取、图片处理和Excel处理的完整流程
"""

import logging
from pathlib import Path
import time

# 导入各个模块
from web_scraper import WebScraper
from adaptive_table_processor import AdaptiveTableProcessor
from excel_post_processor import ExcelPostProcessor
from cleanup_files import cleanup_intermediate_files


# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MainController:
    """主控制器"""
    
    def __init__(self):
        self.current_dir = Path(__file__).parent
        self.images_dir = self.current_dir / "images"
        self.output_dir = self.current_dir / "output"

        # 初始化各个处理器
        self.web_scraper = WebScraper()
        self.table_processor = AdaptiveTableProcessor()
        self.post_processor = ExcelPostProcessor()

        logger.info("MainController 初始化完成")

    def merge_excel_files(self, excel_files, output_path):
        """合并多个Excel文件 - 直接按列顺序上下叠加"""
        try:
            import pandas as pd

            logger.info(f"开始合并 {len(excel_files)} 个Excel文件")

            all_data = []

            for i, excel_file in enumerate(excel_files):
                try:
                    df = pd.read_excel(excel_file)
                    if not df.empty:
                        logger.debug(f"读取文件 {i+1}: {excel_file}, 行数: {len(df)}, 列数: {len(df.columns)}")

                        # 如果不是第一个文件，统一列名为第一个文件的列名（确保按列顺序对齐）
                        if i > 0 and all_data:
                            first_df_columns = all_data[0].columns
                            df.columns = first_df_columns  # 直接使用第一个文件的列名
                            logger.debug(f"文件 {i+1} 列名已统一为第一个文件的列名")

                        all_data.append(df)
                    else:
                        logger.warning(f"文件为空: {excel_file}")
                except Exception as e:
                    logger.error(f"读取文件失败 {excel_file}: {e}")

            if not all_data:
                logger.error("没有有效的数据可以合并")
                return False

            # 直接按列顺序上下叠加
            merged_df = pd.concat(all_data, ignore_index=True)

            # 保存合并后的文件
            merged_df.to_excel(output_path, index=False)

            logger.info(f"Excel文件合并完成: {output_path}")
            logger.info(f"合并后: 总行数={len(merged_df)}, 总列数={len(merged_df.columns)}")

            return True

        except Exception as e:
            logger.error(f"合并Excel文件失败: {e}")
            return False

    def run_complete_workflow(self):
        """运行完整的工作流程"""
        try:
            logger.info("="*80)
            logger.info("🚀 开始完整的数据处理工作流程")
            logger.info("="*80)
            
            start_time = time.time()
            
            # 第1步：检查现有图片
            logger.info("📋 第1步：检查现有图片文件")
            images_dir = Path("images")
            scraped_images = []

            # 查找现有的图片文件
            for img_file in images_dir.glob("*.png"):
                if img_file.name.startswith("2025-08"):
                    scraped_images.append(img_file)

            if not scraped_images:
                logger.error("❌ 未找到图片文件，无法继续")
                return False

            logger.info(f"✅ 找到 {len(scraped_images)} 个图片文件: {[img.name for img in scraped_images]}")
            
            # 第2步：图片处理
            logger.info("📋 第2步：处理图片并生成Excel")
            processed_count = 0
            excel_files = []

            # 初始化自适应表格处理器
            processor = AdaptiveTableProcessor()

            # 按日期分组处理图片
            from collections import defaultdict
            date_groups = defaultdict(list)

            for image_path in scraped_images:
                image_name = Path(image_path).stem
                # 提取日期部分（假设格式为 2025-08-13-1, 2025-08-13-2）
                date_part = '-'.join(image_name.split('-')[:3])  # 取前3部分作为日期
                date_groups[date_part].append(image_path)

            for date, images in date_groups.items():
                logger.info(f"处理日期 {date} 的 {len(images)} 张图片")

                if len(images) == 1:
                    # 单张图片，直接处理
                    image_path = images[0]
                    logger.info(f"处理单张图片: {Path(image_path).name}")

                    output_path = self.output_dir / f"{date}.xlsx"
                    success = processor.process_complete_workflow(image_path, output_path)

                    if success:
                        processed_count += 1
                        excel_files.append(output_path)
                        logger.info(f"✅ 图片处理成功: {output_path}")
                    else:
                        logger.error(f"❌ 图片处理失败: {image_path}")

                else:
                    # 多张图片，需要合并
                    logger.info(f"处理多张图片: {[Path(img).name for img in images]}")
                    temp_excel_files = []

                    # 先分别处理每张图片
                    for i, image_path in enumerate(images, 1):
                        temp_output = self.output_dir / f"{date}-{i}_temp.xlsx"
                        success = processor.process_complete_workflow(image_path, temp_output)

                        if success:
                            temp_excel_files.append(temp_output)
                            logger.info(f"✅ 临时文件生成: {temp_output}")
                        else:
                            logger.error(f"❌ 图片处理失败: {image_path}")

                    # 合并Excel文件
                    if temp_excel_files:
                        merged_output = self.output_dir / f"{date}.xlsx"
                        success = self.merge_excel_files(temp_excel_files, merged_output)

                        if success:
                            processed_count += len(images)
                            excel_files.append(merged_output)
                            logger.info(f"✅ Excel文件合并成功: {merged_output}")

                            # 保留临时文件供检查
                            logger.info(f"📋 保留临时文件供检查:")
                            for temp_file in temp_excel_files:
                                logger.info(f"   📄 {temp_file}")
                        else:
                            logger.error(f"❌ Excel文件合并失败: {date}")

            if processed_count == 0:
                logger.error("❌ 所有图片处理失败，无法继续")
                return False

            logger.info(f"✅ 图片处理完成，成功处理 {processed_count} 张图片，生成 {len(excel_files)} 个Excel文件")
            
            # 处理完成
            logger.info(f"✅ 数据处理完成，生成 {len(excel_files)} 个Excel文件")
            
            # 统计信息
            total_time = time.time() - start_time
            
            logger.info("="*80)
            logger.info("🎉 完整工作流程执行完成！")
            logger.info(f"📊 处理统计:")
            logger.info(f"   爬取图片数: {len(scraped_images)}")
            logger.info(f"   成功处理图片: {processed_count}")
            logger.info(f"   生成Excel文件: {len(excel_files)}")
            logger.info(f"   总处理时间: {total_time:.1f}秒")
            logger.info("📁 输出文件:")
            for file_path in excel_files:
                logger.info(f"   📄 {file_path}")
            logger.info("="*80)
            
            return True

        except Exception as e:
            logger.error(f"完整工作流程执行失败: {e}")
            return False

    def run_with_post_processing(self, cleanup=True):
        """运行完整工作流程包括后处理和清理"""

        logger.info("="*60)
        logger.info("🚀 开始完整的股票数据处理工作流程（含后处理）")
        logger.info("="*60)

        start_time = time.time()

        try:
            # 第1步：主要数据处理（爬取、OCR、合并）
            logger.info("📋 第1步：数据爬取和处理")
            success = self.run_complete_workflow()

            if not success:
                logger.error("❌ 主要数据处理失败")
                return False

            # 第2步：Excel后处理
            logger.info("📋 第2步：Excel后处理")
            post_success = self.run_post_processing()

            if not post_success:
                logger.error("❌ Excel后处理失败")
                return False

            # 第3步：清理中间文件
            if cleanup:
                logger.info("📋 第3步：清理中间文件")
                deleted_count = cleanup_intermediate_files()
                logger.info(f"✅ 清理了 {deleted_count} 个中间文件")

            # 显示最终结果
            total_time = time.time() - start_time

            logger.info("="*60)
            logger.info("🎉 完整工作流程执行成功！")
            logger.info("="*60)

            # 显示最终保留的文件
            self._show_final_files()

            logger.info(f"⏱️ 总处理时间: {total_time:.1f}秒")
            logger.info("="*60)

            return True

        except Exception as e:
            logger.error(f"❌ 完整工作流程执行失败: {e}")
            return False

    def run_post_processing(self):
        """运行Excel后处理"""

        try:
            # 查找最新的合并文件
            excel_files = list(self.output_dir.glob("2025-*.xlsx"))
            merged_files = [f for f in excel_files if "_temp" not in f.name and "_processed" not in f.name]

            if not merged_files:
                logger.error("❌ 未找到合并的Excel文件")
                return False

            input_file = max(merged_files, key=lambda x: x.stat().st_mtime)
            output_file = input_file.parent / f"{input_file.stem}_processed{input_file.suffix}"

            logger.info(f"输入文件: {input_file.name}")
            logger.info(f"输出文件: {output_file.name}")

            success = self.post_processor.process_excel_file(input_file, output_file)

            if success:
                logger.info("✅ Excel后处理完成")
            else:
                logger.error("❌ Excel后处理失败")

            return success

        except Exception as e:
            logger.error(f"Excel后处理失败: {e}")
            return False

    def run_cleanup(self):
        """运行文件清理"""

        try:
            logger.info("📋 开始文件清理")
            deleted_count = cleanup_intermediate_files()

            if deleted_count > 0:
                logger.info(f"✅ 清理完成，删除了 {deleted_count} 个文件")
            else:
                logger.info("ℹ️ 没有需要清理的文件")

            return True

        except Exception as e:
            logger.error(f"文件清理失败: {e}")
            return False

    def _show_final_files(self):
        """显示最终保留的文件"""

        logger.info("📁 最终输出文件:")

        # 原始图片
        if self.images_dir.exists():
            png_files = list(self.images_dir.glob("*.png"))
            for png_file in png_files:
                logger.info(f"   📷 {png_file.name}")

        # 最终处理文件
        if self.output_dir.exists():
            processed_files = list(self.output_dir.glob("*_processed.xlsx"))
            for processed_file in processed_files:
                logger.info(f"   📊 {processed_file.name}")

def main():
    """主函数"""

    import sys

    try:
        controller = MainController()

        # 检查命令行参数
        if len(sys.argv) > 1:
            command = sys.argv[1].lower()

            if command == "post":
                # 仅运行后处理
                success = controller.run_post_processing()
            elif command == "cleanup":
                # 仅运行清理
                success = controller.run_cleanup()
            elif command == "full":
                # 运行完整流程+后处理+清理
                success = controller.run_with_post_processing(cleanup=True)
            elif command == "no-cleanup":
                # 运行完整流程+后处理但不清理
                success = controller.run_with_post_processing(cleanup=False)
            elif command == "basic":
                # 仅运行基础流程（原有功能）
                success = controller.run_complete_workflow()
            else:
                logger.error("❌ 无效的命令参数")
                logger.info("用法:")
                logger.info("  python main_controller.py basic      # 基础流程（爬取+OCR+合并）")
                logger.info("  python main_controller.py full       # 完整流程+后处理+清理")
                logger.info("  python main_controller.py no-cleanup # 完整流程+后处理不清理")
                logger.info("  python main_controller.py post       # 仅后处理")
                logger.info("  python main_controller.py cleanup    # 仅清理")
                return False
        else:
            # 默认运行完整流程+后处理+清理
            success = controller.run_with_post_processing(cleanup=True)

        if success:
            logger.info("🎉 所有任务执行成功！")
        else:
            logger.error("❌ 任务执行失败")

        return success

    except Exception as e:
        logger.error(f"主程序执行失败: {e}")
        return False

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)

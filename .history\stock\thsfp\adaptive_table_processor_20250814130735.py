#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自适应表格处理器
根据图像尺寸自动计算切分块数，精准切分后使用有线表格识别，最终输出Excel
"""

import cv2
import numpy as np
from pathlib import Path
import logging
import json
import time
import math
import pandas as pd
from bs4 import BeautifulSoup
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AdaptiveTableProcessor:
    """自适应表格处理器"""
    
    def __init__(self):
        self.max_dimension = 1500      # 最大处理尺寸
        self.min_scale_factor = 0.9    # 最小缩放比例
        self.max_scale_factor = 1.0    # 最大缩放比例
        self.current_dir = Path(__file__).parent
        self.split_images_dir = self.current_dir / "images" / "split_images"
        
        # 初始化标志
        self.engines_initialized = False
        self.wired_engine = None
        self.ocr_engine = None
        
        logger.info("AdaptiveTableProcessor 初始化完成")
        logger.info(f"最大处理尺寸: {self.max_dimension}px")
        logger.info(f"最小缩放比例: {self.min_scale_factor}")
    
    def calculate_optimal_segments(self, image_height, image_width):
        """根据图像尺寸自动计算最优切分块数"""
        try:
            # 根据缩放比例限制计算最大允许的片段尺寸
            max_segment_height = int(self.max_dimension / self.min_scale_factor)
            max_segment_width = int(self.max_dimension / self.min_scale_factor)
            
            # 计算需要的切分块数
            height_segments = max(1, int(np.ceil(image_height / max_segment_height)))
            width_segments = max(1, int(np.ceil(image_width / max_segment_width)))
            
            # 由于我们主要按高度切分，使用高度切分数
            optimal_segments = height_segments
            
            # 计算实际的片段高度
            actual_segment_height = image_height / optimal_segments
            actual_scale_factor = self.max_dimension / max(actual_segment_height, image_width)
            
            logger.info(f"图像尺寸: {image_width} x {image_height}")
            logger.info(f"最大允许片段高度: {max_segment_height} (缩放比例≥{self.min_scale_factor})")
            logger.info(f"计算得出最优切分块数: {optimal_segments}")
            logger.info(f"实际片段高度: {actual_segment_height:.0f}")
            logger.info(f"预期缩放比例: {actual_scale_factor:.3f}")
            
            return optimal_segments, int(actual_segment_height)
            
        except Exception as e:
            logger.error(f"计算最优切分块数失败: {e}")
            return 1, image_height
    
    def initialize_engines(self):
        """初始化TableStructureRec引擎"""
        if self.engines_initialized:
            return
        
        try:
            logger.info("正在初始化TableStructureRec引擎...")

            from wired_table_rec.main import WiredTableInput, WiredTableRecognition
            from rapidocr import RapidOCR

            # 初始化有线表格识别引擎 - 使用unet模型
            wired_input = WiredTableInput(model_type="unet")
            self.wired_engine = WiredTableRecognition(wired_input)
            logger.info("✅ 使用有线表格识别引擎 (unet模型)")

            # 初始化OCR引擎
            self.ocr_engine = RapidOCR()

            self.engines_initialized = True
            logger.info("✅ TableStructureRec引擎初始化成功")
            
        except ImportError as e:
            logger.error(f"❌ 缺少必要的库: {e}")
            raise
        except Exception as e:
            logger.error(f"❌ 引擎初始化失败: {e}")
            raise
    
    def detect_thick_horizontal_lines(self, image):
        """改进的粗横线检测 - 结合多种方法提高覆盖率到90%"""
        try:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            height, width = gray.shape

            # 创建结果掩码
            result_mask = np.zeros_like(gray)

            # 方法1: 形态学操作检测（原有方法增强）
            horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (width//15, 2))
            horizontal_lines = cv2.morphologyEx(gray, cv2.MORPH_OPEN, horizontal_kernel)
            result_mask = cv2.bitwise_or(result_mask, horizontal_lines)

            # 方法2: 改进的Canny + HoughLinesP
            edges = cv2.Canny(gray, 20, 80, apertureSize=3)
            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=30,
                                   minLineLength=width*0.25, maxLineGap=15)

            if lines is not None:
                line_mask = np.zeros_like(gray)
                for line in lines:
                    x1, y1, x2, y2 = line[0]
                    # 更严格的水平线检查（角度小于3度）
                    if abs(y2 - y1) <= max(1, abs(x2 - x1) * 0.052):  # tan(3°) ≈ 0.052
                        cv2.line(line_mask, (x1, y1), (x2, y2), 255, 2)
                result_mask = cv2.bitwise_or(result_mask, line_mask)

            # 方法3: 基于像素密度的行扫描
            row_dark_density = []
            for y in range(height):
                dark_pixels = np.sum(gray[y, :] < 100)
                density = dark_pixels / width
                row_dark_density.append(density)

            # 自适应阈值
            mean_density = np.mean(row_dark_density)
            std_density = np.std(row_dark_density)
            density_threshold = mean_density + 1.0 * std_density

            for y in range(height):
                if row_dark_density[y] > density_threshold:
                    # 检查连续性
                    consecutive = 1
                    for dy in range(1, 3):
                        if y + dy < height and row_dark_density[y + dy] > density_threshold * 0.8:
                            consecutive += 1
                        else:
                            break

                    if consecutive >= 2:
                        for dy in range(consecutive):
                            if y + dy < height:
                                result_mask[y + dy, :] = 255

            # 方法4: 基于梯度的水平边缘检测
            sobel_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
            sobel_y_abs = np.abs(sobel_y)

            gradient_threshold = np.mean(sobel_y_abs) + 1.2 * np.std(sobel_y_abs)
            strong_edges = sobel_y_abs > gradient_threshold

            for y in range(height):
                edge_pixels = np.sum(strong_edges[y, :])
                if edge_pixels > width * 0.15:
                    result_mask[y, :] = 255

            # 后处理：连接和清理
            connect_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (8, 1))
            result_mask = cv2.morphologyEx(result_mask, cv2.MORPH_CLOSE, connect_kernel)

            clean_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (width//25, 1))
            result_mask = cv2.morphologyEx(result_mask, cv2.MORPH_OPEN, clean_kernel)

            # 统计检测结果
            line_rows = np.sum(result_mask > 0, axis=1)
            detected_lines = np.sum(line_rows > width * 0.1)
            coverage = detected_lines / height * 100
            logger.info(f"改进线条检测: 检测到 {detected_lines} 行包含线条，覆盖率: {coverage:.1f}%")

            return result_mask

        except Exception as e:
            logger.error(f"检测粗横线失败: {e}")
            return None
    
    def find_precise_split_lines(self, image, line_mask, target_segments, target_height):
        """找到精准的切分线位置"""
        try:
            height, width = image.shape[:2]
            
            # 分析每行的线条强度
            row_line_strength = []
            for y in range(height):
                line_pixels = np.sum(line_mask[y, :] > 0)
                strength = line_pixels / width
                row_line_strength.append(strength)
            
            # 找到线条强度较高的行 - 调整阈值以检测真正的表格线
            mean_strength = np.mean(row_line_strength)
            std_strength = np.std(row_line_strength)
            max_strength = np.max(row_line_strength)

            # 使用更合理的阈值，检测明显的表格分隔线
            # 使用百分位数方法，选择前20%最强的线条
            percentile_80 = np.percentile(row_line_strength, 80)
            threshold = max(0.03, percentile_80)  # 至少3%的像素覆盖，或者80%分位数
            strong_line_rows = [(y, strength) for y, strength in enumerate(row_line_strength) if strength > threshold]

            # 添加调试信息
            logger.info(f"线条强度统计: 平均={mean_strength:.4f}, 标准差={std_strength:.4f}, 最大值={max_strength:.4f}")
            logger.info(f"检测阈值: {threshold:.4f}")
            logger.info(f"超过阈值的行数: {len(strong_line_rows)}")
            
            logger.info(f"检测到 {len(strong_line_rows)} 个强线条候选位置")
            
            if not strong_line_rows:
                logger.warning("未检测到明显的粗横线，使用均匀分割")
                return self.uniform_split_positions(height, target_segments)
            
            # 合并相近的线条
            merged_lines = self.merge_nearby_lines(strong_line_rows)
            logger.info(f"合并后剩余 {len(merged_lines)} 个线条位置")
            
            # 选择最佳的切分位置
            split_positions = self.select_optimal_split_positions(merged_lines, height, target_segments, target_height)
            
            return split_positions
            
        except Exception as e:
            logger.error(f"查找精准切分线失败: {e}")
            return self.uniform_split_positions(height, target_segments)
    
    def merge_nearby_lines(self, line_rows, merge_distance=15):
        """合并相近的线条"""
        if not line_rows:
            return []
        
        line_rows.sort(key=lambda x: x[0])
        merged = []
        current_group = [line_rows[0]]
        
        for i in range(1, len(line_rows)):
            current_y, current_strength = line_rows[i]
            last_y, last_strength = current_group[-1]
            
            if current_y - last_y <= merge_distance:
                current_group.append(line_rows[i])
            else:
                best_line = max(current_group, key=lambda x: x[1])
                merged.append(best_line[0])
                current_group = [line_rows[i]]
        
        if current_group:
            best_line = max(current_group, key=lambda x: x[1])
            merged.append(best_line[0])
        
        return merged
    
    def select_optimal_split_positions(self, line_positions, total_height, target_segments, target_height):
        """选择最优的切分位置"""
        if not line_positions:
            return self.uniform_split_positions(total_height, target_segments)
        
        # 计算理想的切分位置
        ideal_positions = []
        for i in range(1, target_segments):
            ideal_pos = i * target_height
            if ideal_pos < total_height:
                ideal_positions.append(ideal_pos)
        
        logger.info(f"理想切分位置: {ideal_positions}")
        logger.info(f"检测到的线条位置: {line_positions}")
        
        # 为每个理想位置找到最近的线条
        selected_positions = []
        used_lines = set()
        
        for ideal_pos in ideal_positions:
            best_line = None
            min_distance = float('inf')
            
            for line_pos in line_positions:
                if line_pos not in used_lines:
                    distance = abs(line_pos - ideal_pos)
                    if distance < min_distance:
                        min_distance = distance
                        best_line = line_pos
            
            # 提高精度要求：偏差必须小于20像素
            if best_line is not None and min_distance < 20:
                selected_positions.append(best_line)
                used_lines.add(best_line)
                logger.info(f"理想位置 {ideal_pos} -> 实际线条位置 {best_line} (偏差: {min_distance})")
            else:
                selected_positions.append(ideal_pos)
                if best_line is not None:
                    logger.info(f"理想位置 {ideal_pos} -> 使用理想位置 (偏差{min_distance}超过20像素)")
                else:
                    logger.info(f"理想位置 {ideal_pos} -> 使用理想位置 (无合适线条)")
        
        return sorted(selected_positions)
    
    def uniform_split_positions(self, height, target_segments):
        """均匀切分位置"""
        positions = []
        segment_height = height // target_segments
        
        for i in range(1, target_segments):
            pos = i * segment_height
            if pos < height:
                positions.append(pos)
        
        return positions

    def detect_all_horizontal_lines(self, image, black_thr=127):
        """基于膨胀算法精确检测表格横线位置"""
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            height, width = gray.shape

            # 转为二值图（白底黑字）
            ret, binary = cv2.threshold(gray, black_thr, 255, cv2.THRESH_BINARY)

            # 膨胀算法的色块大小
            hors_k = int(math.sqrt(width) * 1.2)
            vert_k = int(math.sqrt(height) * 1.2)

            logger.debug(f"膨胀核大小: 横向={hors_k}, 纵向={vert_k}")

            # 白底黑字，膨胀白色横向色块，抹去文字和竖线，保留横线
            kernel_h = cv2.getStructuringElement(cv2.MORPH_RECT, (hors_k, 1))
            hors = ~cv2.dilate(binary, kernel_h, iterations=1)  # 变反为黑底白线

            # 白底黑字，膨胀白色竖向色块，抹去文字和横线，保留竖线
            kernel_v = cv2.getStructuringElement(cv2.MORPH_RECT, (1, vert_k))
            verts = ~cv2.dilate(binary, kernel_v, iterations=1)  # 变反为黑底白线

            # 横线竖线检测结果合并
            borders = cv2.bitwise_or(hors, verts)

            # 从横线检测结果中提取横线位置
            line_positions = []

            # 分析每一行，找到横线
            for y in range(height):
                row = hors[y, :]
                white_pixels = np.sum(row == 255)  # 白色像素（横线）
                white_ratio = white_pixels / width

                # 如果该行有足够多的白色像素，认为是横线
                if white_ratio > 0.5:  # 至少50%的像素为白色（横线）
                    line_positions.append(y)

            # 合并相邻的横线位置（横线可能有厚度）
            merged_positions = []
            if line_positions:
                current_group = [line_positions[0]]

                for pos in line_positions[1:]:
                    if pos - current_group[-1] <= 5:  # 5像素内的认为是同一条线
                        current_group.append(pos)
                    else:
                        # 取当前组的中位数作为横线位置
                        merged_positions.append(int(np.median(current_group)))
                        current_group = [pos]

                # 处理最后一组
                merged_positions.append(int(np.median(current_group)))

            # 过滤掉太靠近边缘的线条
            filtered_positions = []
            for pos in merged_positions:
                if 20 < pos < height - 20:  # 距离边缘至少20像素
                    filtered_positions.append(pos)

            logger.info(f"膨胀算法检测到 {len(filtered_positions)} 条精确的表格横线")

            # 可选：保存调试图像
            debug_dir = Path("debug_images")
            debug_dir.mkdir(exist_ok=True)
            cv2.imwrite(str(debug_dir / "binary.png"), binary)
            cv2.imwrite(str(debug_dir / "horizontal_lines.png"), hors)
            cv2.imwrite(str(debug_dir / "vertical_lines.png"), verts)
            cv2.imwrite(str(debug_dir / "borders.png"), borders)

            return filtered_positions

        except Exception as e:
            logger.error(f"膨胀算法检测横线失败: {e}")
            return []

    def find_optimal_split_positions_on_lines(self, line_positions, image_height):
        """改进的切分策略：预估切分数量+2，平均按横线切分"""
        try:
            if not line_positions:
                return []

            # 第1步：预估需要的切分数量
            # 由于宽度固定，根据高度估算需要多少个小于1500px的片段
            estimated_segments = math.ceil(image_height / 1500)
            # 在预估基础上增加2个，确保每个片段都小于1500px
            target_segments = estimated_segments + 2

            logger.info(f"图片高度: {image_height}px")
            logger.info(f"预估需要 {estimated_segments} 个片段")
            logger.info(f"目标切分数量: {target_segments} 个片段 (预估+2)")
            logger.info(f"目标片段高度: {image_height / target_segments:.0f}px")

            # 第2步：分析横线间距
            line_distances = []
            for i in range(len(line_positions) - 1):
                distance = line_positions[i + 1] - line_positions[i]
                line_distances.append(distance)

            if line_distances:
                avg_distance = np.mean(line_distances)
                std_distance = np.std(line_distances)
                logger.info(f"横线间距统计: 平均={avg_distance:.1f}, 标准差={std_distance:.1f}")

            # 第3步：平均按横线切分
            # 需要 target_segments-1 个切分点
            needed_splits = target_segments - 1

            if len(line_positions) < needed_splits:
                logger.warning(f"横线数量不足: 需要{needed_splits}个切分点，但只有{len(line_positions)}条横线")
                # 使用所有可用的横线
                split_positions = line_positions[:-1]  # 排除最后一条线
            else:
                # 平均分布选择横线
                split_positions = []
                segment_height = image_height / target_segments

                for i in range(1, target_segments):
                    ideal_pos = i * segment_height

                    # 找到最接近理想位置的横线
                    available_lines = [pos for pos in line_positions if pos not in split_positions]
                    if available_lines:
                        closest_line = min(available_lines, key=lambda x: abs(x - ideal_pos))
                        split_positions.append(closest_line)

                        deviation = abs(closest_line - ideal_pos)
                        logger.info(f"片段 {i}: 理想位置 {ideal_pos:.0f} -> 横线位置 {closest_line} (偏差: {deviation:.0f})")
                    else:
                        logger.warning(f"片段 {i}: 无可用横线，使用理想位置 {ideal_pos:.0f}")
                        split_positions.append(int(ideal_pos))

            # 第4步：确保切分位置在合理范围内
            valid_splits = []
            for pos in sorted(split_positions):
                if 50 < pos < image_height - 50:  # 距离边缘至少50像素
                    valid_splits.append(pos)

            # 第5步：验证切分结果
            all_positions = [0] + valid_splits + [image_height]

            logger.info(f"最终切分方案:")
            max_height = 0
            all_under_1500 = True

            for i in range(len(all_positions) - 1):
                start = all_positions[i]
                end = all_positions[i + 1]
                height = end - start
                scale = self.max_dimension / height
                max_height = max(max_height, height)

                if height > 1500:
                    all_under_1500 = False
                    status = "❌ 超过1500px"
                elif height < 1000:
                    status = "⚠️ 过小"
                else:
                    status = "✅ 合适"

                logger.info(f"片段 {i+1}: {start}-{end} (高度:{height}px, 缩放:{scale:.3f}) {status}")

            logger.info(f"最大片段高度: {max_height}px")
            logger.info(f"所有片段都小于1500px: {'是' if all_under_1500 else '否'}")
            logger.info(f"最终选择 {len(valid_splits)} 个切分点")

            return valid_splits

        except Exception as e:
            logger.error(f"改进切分策略失败: {e}")
            return []

    def split_image_adaptively(self, image_path):
        """自适应切分图像"""
        try:
            logger.info(f"开始自适应图像切分: {image_path}")
            
            # 读取图像
            image = cv2.imread(str(image_path))
            if image is None:
                logger.error(f"无法读取图像: {image_path}")
                return []
            
            height, width = image.shape[:2]
            logger.info(f"原始图像尺寸: {width} x {height}")
            
            # 检测所有横线位置
            line_positions = self.detect_all_horizontal_lines(image)

            if not line_positions:
                logger.warning("未检测到横线，使用均匀切分")
                # 计算最优切分块数
                target_segments, _ = self.calculate_optimal_segments(height, width)
                split_positions = self.uniform_split_positions(height, target_segments)
            else:
                # 基于横线位置进行智能切分（横线优先）
                split_positions = self.find_optimal_split_positions_on_lines(line_positions, height)
            
            # 执行切分
            segments = []
            last_y = 0
            
            for i, split_y in enumerate(split_positions):
                segment = image[last_y:split_y, :]
                segments.append(segment)
                
                segment_height = split_y - last_y
                scale_factor = self.max_dimension / max(segment.shape[:2])
                
                logger.info(f"片段 {len(segments)}: {last_y}-{split_y} (高度: {segment_height}, 缩放: {scale_factor:.3f})")
                last_y = split_y
            
            # 处理最后一个片段
            if height - last_y > 100:
                final_segment = image[last_y:height, :]
                segments.append(final_segment)
                
                segment_height = height - last_y
                scale_factor = self.max_dimension / max(final_segment.shape[:2])
                
                logger.info(f"最后片段: {last_y}-{height} (高度: {segment_height}, 缩放: {scale_factor:.3f})")
            
            logger.info(f"总共切分出 {len(segments)} 个片段")

            # 验证缩放比例
            for i, segment in enumerate(segments):
                max_dim = max(segment.shape[:2])
                scale_factor = self.max_dimension / max_dim
                if scale_factor < self.min_scale_factor:
                    logger.warning(f"片段 {i+1} 缩放比例 {scale_factor:.3f} 小于最小要求 {self.min_scale_factor}")
                else:
                    logger.info(f"片段 {i+1} 缩放比例 {scale_factor:.3f} ✅")

            # 保存分割的图片到split_images文件夹
            if segments:
                # 创建split_images目录
                self.split_images_dir.mkdir(parents=True, exist_ok=True)

                # 获取原图片名称（不含扩展名）
                image_name = Path(image_path).stem

                # 为每个片段创建子目录
                segment_dir = self.split_images_dir / image_name
                segment_dir.mkdir(parents=True, exist_ok=True)

                # 保存每个片段
                for i, segment in enumerate(segments):
                    segment_filename = f"segment_{i+1:02d}.png"
                    segment_path = segment_dir / segment_filename

                    cv2.imwrite(str(segment_path), segment)
                    height, width = segment.shape[:2]
                    logger.info(f"保存分割图片: {segment_path} ({width}x{height})")

                logger.info(f"✅ 所有分割图片已保存到: {segment_dir}")

            return segments
            
        except Exception as e:
            logger.error(f"自适应切分失败: {e}")
            return []
    
    def save_segments(self, segments, output_dir):
        """保存切分后的片段"""
        try:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            saved_files = []
            
            for i, segment in enumerate(segments):
                filename = f"adaptive_segment_{i+1:02d}.png"
                file_path = output_path / filename
                
                cv2.imwrite(str(file_path), segment)
                saved_files.append(str(file_path))
                
                height, width = segment.shape[:2]
                scale_factor = self.max_dimension / max(height, width)
                
                logger.info(f"保存片段 {i+1}: {filename} ({width}x{height}, 缩放: {scale_factor:.3f})")
            
            return saved_files
            
        except Exception as e:
            logger.error(f"保存片段失败: {e}")
            return []

    def preprocess_image(self, image_path):
        """图像预处理 - 分辨率优化"""
        try:
            if isinstance(image_path, (str, Path)):
                image = cv2.imread(str(image_path))
                if image is None:
                    raise ValueError(f"无法读取图像: {image_path}")
            else:
                image = image_path

            original_height, original_width = image.shape[:2]
            max_dimension = max(original_height, original_width)

            if max_dimension > self.max_dimension:
                scale = self.max_dimension / max_dimension
                new_width = int(original_width * scale)
                new_height = int(original_height * scale)

                resized_image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
                logger.info(f"图像已缩放至: {new_width} x {new_height} (缩放比例: {scale:.3f})")

                return resized_image, scale
            else:
                logger.info("图像尺寸在限制范围内，无需缩放")
                return image, 1.0

        except Exception as e:
            logger.error(f"图像预处理失败: {e}")
            raise

    def perform_ocr(self, image):
        """执行OCR识别"""
        try:
            start_time = time.time()

            rapid_ocr_output = self.ocr_engine(image, return_word_box=True)

            if rapid_ocr_output is None or not hasattr(rapid_ocr_output, 'boxes'):
                logger.warning("OCR识别结果为空")
                return []

            ocr_result = list(zip(
                rapid_ocr_output.boxes,
                rapid_ocr_output.txts,
                rapid_ocr_output.scores
            ))

            elapsed = time.time() - start_time
            logger.info(f"OCR识别完成: 检测到 {len(ocr_result)} 个文本框 (耗时: {elapsed:.3f}s)")

            return ocr_result

        except Exception as e:
            logger.error(f"OCR识别失败: {e}")
            return []

    def recognize_table_structure(self, image, ocr_result):
        """表格结构识别 - 强制使用有线表格识别"""
        try:
            start_time = time.time()

            logger.info("使用有线表格识别引擎 (wired_table_rec_v2)")

            table_results = self.wired_engine(
                image,
                ocr_result=ocr_result,
                enhance_box_line=True,
                rotated_fix=True,
                need_ocr=True,
            )

            elapsed = time.time() - start_time
            cell_count = len(table_results.cell_bboxes) if table_results.cell_bboxes is not None else 0

            logger.info(f"表格结构识别完成: 检测到 {cell_count} 个单元格 (耗时: {elapsed:.3f}s)")

            return table_results

        except Exception as e:
            logger.error(f"表格结构识别失败: {e}")
            return None

    def process_single_segment(self, segment_image, segment_index):
        """处理单个片段"""
        try:
            logger.info(f"🔄 处理片段 {segment_index}")

            # 确保引擎已初始化
            self.initialize_engines()

            # 图像预处理
            processed_image, scale_factor = self.preprocess_image(segment_image)

            # OCR识别
            ocr_result = self.perform_ocr(processed_image)

            # 表格结构识别
            table_results = self.recognize_table_structure(processed_image, ocr_result)

            if table_results is None:
                logger.error(f"片段 {segment_index} 表格结构识别失败")
                return None

            return {
                "segment_index": segment_index,
                "scale_factor": scale_factor,
                "cell_count": len(table_results.cell_bboxes) if table_results.cell_bboxes is not None else 0,
                "ocr_text_count": len(ocr_result),
                "processing_time": table_results.elapse,
                "html_content": table_results.pred_html,
                "table_results": table_results
            }

        except Exception as e:
            logger.error(f"处理片段 {segment_index} 失败: {e}")
            return None

    def html_to_dataframe(self, html_content):
        """将HTML表格转换为DataFrame"""
        try:
            if not html_content or html_content.strip() == "":
                logger.warning("HTML内容为空")
                return pd.DataFrame()

            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(html_content, 'html.parser')
            table = soup.find('table')

            if table is None:
                logger.warning("HTML中未找到表格")
                return pd.DataFrame()

            # 提取表格数据
            rows = []
            for tr in table.find_all('tr'):
                row = []
                for cell in tr.find_all(['td', 'th']):
                    # 获取单元格文本，处理换行符
                    cell_text = cell.get_text(strip=True)
                    cell_text = re.sub(r'\s+', ' ', cell_text)  # 合并多个空白字符
                    row.append(cell_text)
                if row:  # 只添加非空行
                    rows.append(row)

            if not rows:
                logger.warning("表格中没有数据行")
                return pd.DataFrame()

            # 确保所有行的列数一致
            max_cols = max(len(row) for row in rows) if rows else 0
            for row in rows:
                while len(row) < max_cols:
                    row.append("")

            # 创建DataFrame
            df = pd.DataFrame(rows)

            logger.info(f"成功转换HTML表格: {len(df)} 行 x {len(df.columns)} 列")
            return df

        except Exception as e:
            logger.error(f"HTML转DataFrame失败: {e}")
            return pd.DataFrame()

    def merge_dataframes(self, dataframes):
        """合并多个DataFrame"""
        try:
            if not dataframes:
                logger.warning("没有DataFrame需要合并")
                return pd.DataFrame()

            # 过滤空的DataFrame
            valid_dfs = [df for df in dataframes if not df.empty]

            if not valid_dfs:
                logger.warning("所有DataFrame都为空")
                return pd.DataFrame()

            # 确保所有DataFrame的列数一致
            max_cols = max(len(df.columns) for df in valid_dfs)

            for df in valid_dfs:
                while len(df.columns) < max_cols:
                    df[len(df.columns)] = ""

            # 合并DataFrame
            merged_df = pd.concat(valid_dfs, ignore_index=True)

            logger.info(f"成功合并 {len(valid_dfs)} 个表格: 总计 {len(merged_df)} 行 x {len(merged_df.columns)} 列")
            return merged_df

        except Exception as e:
            logger.error(f"合并DataFrame失败: {e}")
            return pd.DataFrame()

    def save_to_excel(self, dataframe, output_path):
        """保存DataFrame到Excel"""
        try:
            if dataframe.empty:
                logger.warning("DataFrame为空，无法保存到Excel")
                return False

            # 创建输出目录
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)

            # 保存到Excel
            with pd.ExcelWriter(str(output_file), engine='openpyxl') as writer:
                dataframe.to_excel(writer, sheet_name='表格数据', index=False, header=False)

                # 获取工作表并设置格式
                worksheet = writer.sheets['表格数据']

                # 自动调整列宽
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter

                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass

                    adjusted_width = min(max_length + 2, 50)  # 限制最大宽度
                    worksheet.column_dimensions[column_letter].width = adjusted_width

            logger.info(f"✅ Excel文件已保存: {output_file}")
            logger.info(f"📊 数据规模: {len(dataframe)} 行 x {len(dataframe.columns)} 列")

            return True

        except Exception as e:
            logger.error(f"保存Excel失败: {e}")
            return False

    def process_complete_workflow(self, image_path, output_excel_path):
        """完整的工作流程：切分 -> 识别 -> 合并 -> 输出Excel"""
        try:
            logger.info("="*60)
            logger.info("🚀 开始自适应表格处理完整工作流程")
            logger.info("="*60)

            start_time = time.time()

            # 第1步：自适应切分图像
            logger.info("📋 第1步：自适应切分图像")
            segments = self.split_image_adaptively(image_path)

            if not segments:
                logger.error("❌ 图像切分失败")
                return False

            # 第2步：处理每个片段
            logger.info(f"📋 第2步：处理 {len(segments)} 个片段")
            segment_results = []
            dataframes = []

            for i, segment in enumerate(segments, 1):
                result = self.process_single_segment(segment, i)
                if result:
                    segment_results.append(result)

                    # 转换HTML为DataFrame
                    df = self.html_to_dataframe(result["html_content"])
                    if not df.empty:
                        dataframes.append(df)
                        logger.info(f"✅ 片段 {i} 处理成功: {result['cell_count']} 个单元格")
                    else:
                        logger.warning(f"⚠️ 片段 {i} 转换DataFrame失败")
                else:
                    logger.error(f"❌ 片段 {i} 处理失败")

            if not dataframes:
                logger.error("❌ 没有成功处理的片段")
                return False

            # 第3步：合并所有DataFrame
            logger.info("📋 第3步：合并表格数据")
            merged_df = self.merge_dataframes(dataframes)

            if merged_df.empty:
                logger.error("❌ 合并DataFrame失败")
                return False

            # 第4步：保存到Excel
            logger.info("📋 第4步：保存到Excel")
            success = self.save_to_excel(merged_df, output_excel_path)

            if not success:
                logger.error("❌ 保存Excel失败")
                return False

            # 统计信息
            total_time = time.time() - start_time
            total_cells = sum(r["cell_count"] for r in segment_results)
            total_ocr_texts = sum(r["ocr_text_count"] for r in segment_results)

            logger.info("="*60)
            logger.info("🎉 自适应表格处理完成！")
            logger.info(f"📊 处理统计:")
            logger.info(f"   切分片段数: {len(segments)}")
            logger.info(f"   成功处理片段: {len(segment_results)}")
            logger.info(f"   总单元格数: {total_cells}")
            logger.info(f"   总OCR文本框: {total_ocr_texts}")
            logger.info(f"   最终表格规模: {len(merged_df)} 行 x {len(merged_df.columns)} 列")
            logger.info(f"   总处理时间: {total_time:.3f}秒")
            logger.info(f"   Excel文件: {output_excel_path}")
            logger.info("="*60)

            return True

        except Exception as e:
            logger.error(f"完整工作流程失败: {e}")
            return False



def main():
    """主函数"""
    try:
        processor = AdaptiveTableProcessor()

        # 查找images目录中的图片文件
        images_dir = processor.current_dir / "images"
        if not images_dir.exists():
            logger.error(f"图像目录不存在: {images_dir}")
            return

        # 查找第一个图片文件
        image_files = list(images_dir.glob("*.png")) + list(images_dir.glob("*.jpg")) + list(images_dir.glob("*.jpeg"))

        if not image_files:
            logger.error(f"在 {images_dir} 目录中未找到图片文件")
            return

        image_path = image_files[0]
        logger.info(f"使用图片文件: {image_path}")

        # 输出Excel路径
        output_excel = processor.current_dir / "output" / f"{image_path.stem}_processed.xlsx"

        # 执行完整工作流程
        success = processor.process_complete_workflow(image_path, output_excel)

        if success:
            logger.info("🎉 处理成功！")
        else:
            logger.error("❌ 处理失败")

    except Exception as e:
        logger.error(f"主程序执行失败: {e}")

if __name__ == "__main__":
    main()

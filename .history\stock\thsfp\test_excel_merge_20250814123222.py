#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Excel合并问题
"""

import pandas as pd
from pathlib import Path

def test_excel_merge():
    """测试Excel合并问题"""
    
    print("="*60)
    print("🧪 测试Excel合并问题")
    print("="*60)
    
    # 检查临时Excel文件
    output_dir = Path("output")
    temp_files = [
        output_dir / "2025-08-13-1_temp.xlsx",
        output_dir / "2025-08-13-2_temp.xlsx"
    ]
    
    print("\n📋 检查临时Excel文件:")
    
    dfs = []
    for i, temp_file in enumerate(temp_files, 1):
        if temp_file.exists():
            try:
                df = pd.read_excel(temp_file)
                dfs.append(df)
                
                print(f"\n📄 文件 {i}: {temp_file.name}")
                print(f"   行数: {len(df)}")
                print(f"   列数: {len(df.columns)}")
                print(f"   列名: {list(df.columns)}")
                
                # 显示前3行数据
                print(f"   前3行数据:")
                for idx, row in df.head(3).iterrows():
                    print(f"   行{idx+1}: {[str(cell)[:20] for cell in row.values]}")
                
            except Exception as e:
                print(f"   ❌ 读取失败: {e}")
        else:
            print(f"   ❌ 文件不存在: {temp_file}")
    
    # 检查合并文件
    merged_file = output_dir / "2025-08-13.xlsx"
    if merged_file.exists():
        try:
            df_merged = pd.read_excel(merged_file)
            print(f"\n📊 合并文件: {merged_file.name}")
            print(f"   行数: {len(df_merged)}")
            print(f"   列数: {len(df_merged.columns)}")
            print(f"   列名: {list(df_merged.columns)}")
            
            # 检查数据分布
            if len(dfs) == 2:
                df1_rows = len(dfs[0])
                df2_rows = len(dfs[1])
                total_expected = df1_rows + df2_rows
                
                print(f"\n🔍 数据分布检查:")
                print(f"   文件1行数: {df1_rows}")
                print(f"   文件2行数: {df2_rows}")
                print(f"   预期总行数: {total_expected}")
                print(f"   实际总行数: {len(df_merged)}")
                
                if len(df_merged) == total_expected:
                    print("   ✅ 行数匹配")
                else:
                    print("   ❌ 行数不匹配")
                
                # 检查列结构是否一致
                print(f"\n🔍 列结构检查:")
                print(f"   文件1列名: {list(dfs[0].columns)}")
                print(f"   文件2列名: {list(dfs[1].columns)}")
                print(f"   合并后列名: {list(df_merged.columns)}")
                
                if list(dfs[0].columns) == list(dfs[1].columns):
                    print("   ✅ 两个文件列名一致")
                else:
                    print("   ⚠️ 两个文件列名不一致")
                
                # 检查合并后的数据
                print(f"\n🔍 合并后数据检查:")
                print(f"   前{df1_rows}行应该来自文件1")
                print(f"   后{df2_rows}行应该来自文件2")
                
                # 显示分界处的数据
                boundary_start = max(0, df1_rows - 2)
                boundary_end = min(len(df_merged), df1_rows + 2)
                
                print(f"   分界处数据 (行{boundary_start+1}-{boundary_end}):")
                for idx in range(boundary_start, boundary_end):
                    if idx < len(df_merged):
                        source = "文件1" if idx < df1_rows else "文件2"
                        row_data = [str(cell)[:15] for cell in df_merged.iloc[idx].values]
                        print(f"   行{idx+1} ({source}): {row_data}")
                
        except Exception as e:
            print(f"   ❌ 读取合并文件失败: {e}")
    else:
        print(f"❌ 合并文件不存在: {merged_file}")
    
    # 测试简单合并
    if len(dfs) == 2:
        print(f"\n🧪 测试简单合并:")
        try:
            # 直接合并，不做任何调整
            simple_merged = pd.concat(dfs, ignore_index=True)
            
            print(f"   简单合并结果:")
            print(f"   行数: {len(simple_merged)}")
            print(f"   列数: {len(simple_merged.columns)}")
            print(f"   列名: {list(simple_merged.columns)}")
            
            # 保存测试文件
            test_file = output_dir / "test_simple_merge.xlsx"
            simple_merged.to_excel(test_file, index=False)
            print(f"   ✅ 测试文件已保存: {test_file}")
            
        except Exception as e:
            print(f"   ❌ 简单合并失败: {e}")
    
    print("\n" + "="*60)
    print("🎉 测试完成")
    print("="*60)

if __name__ == "__main__":
    test_excel_merge()

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
股票数据定时任务调度器 - 简化版
支持定时执行股票相关脚本，自动检测当天执行状态
"""

import os
import sys
import json
import time
import signal
import logging
import subprocess
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional

try:
    import schedule
except ImportError:
    print("请安装schedule库: pip install schedule")
    sys.exit(1)


class SimpleStockScheduler:
    """简化的股票数据定时任务调度器"""
    
    def __init__(self, config_file: str = "config.json"):
        """初始化调度器"""
        # 如果是相对路径，则相对于脚本所在目录
        if not Path(config_file).is_absolute():
            script_dir = Path(__file__).parent
            self.config_file = script_dir / config_file
        else:
            self.config_file = Path(config_file)
        self.config = self._load_config()
        self.running = False
        self.startup_completed = False
        
        # 设置日志
        self._setup_logging()
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        self.logger.info("📅 股票数据定时任务调度器已初始化")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if not self.config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_file}")
        
        with open(self.config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _setup_logging(self):
        """设置日志"""
        log_file = self.config["settings"]["log_file"]
        
        # 创建日志目录
        log_path = Path(log_file)
        log_path.parent.mkdir(exist_ok=True)
        
        # 配置日志格式
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {signum}，正在停止调度器...")
        self.stop()
    
    def _is_trading_day(self) -> bool:
        """判断是否为交易日（简单实现，排除周末）"""
        if not self.config["settings"]["weekend_skip"]:
            return True
        
        today = datetime.now()
        return today.weekday() < 5  # Monday to Friday
    
    def _get_task_log_key(self, task_name: str, date: str = None) -> str:
        """获取任务日志键"""
        if date is None:
            date = datetime.now().strftime('%Y-%m-%d')
        return f"{task_name}_{date}"
    
    def _is_task_completed_today(self, task_name: str) -> bool:
        """检查任务今天是否已完成"""
        log_key = self._get_task_log_key(task_name)
        log_file = Path(f"{task_name}_status.log")
        
        if not log_file.exists():
            return False
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if log_key in line and "SUCCESS" in line:
                        return True
        except Exception:
            pass
        
        return False
    
    def _mark_task_completed(self, task_name: str, success: bool):
        """标记任务完成状态"""
        log_key = self._get_task_log_key(task_name)
        status = "SUCCESS" if success else "FAILED"
        log_file = Path(f"{task_name}_status.log")
        
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(f"{datetime.now().isoformat()} - {log_key} - {status}\n")
    
    def _execute_script(self, task: Dict[str, Any]) -> bool:
        """执行脚本"""
        try:
            # 构建脚本路径
            project_root = Path(self.config["settings"]["project_root"])
            script_path = project_root / task["script_path"] / task["script_file"]
            
            if not script_path.exists():
                self.logger.error(f"脚本文件不存在: {script_path}")
                return False
            
            # 处理参数中的变量替换
            args = []
            for arg in task.get("args", []):
                if arg == "{today}":
                    args.append(datetime.now().strftime('%Y-%m-%d'))
                else:
                    args.append(arg)
            
            # 构建命令
            command = [sys.executable, str(script_path)] + args
            cwd = script_path.parent
            
            self.logger.info(f"执行命令: {' '.join(command)}")
            self.logger.info(f"工作目录: {cwd}")
            
            # 执行命令
            process = subprocess.Popen(
                command,
                cwd=str(cwd),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                errors='ignore'  # 忽略编码错误
            )
            
            # 等待完成
            stdout, stderr = process.communicate(timeout=task.get("timeout", 3600))
            
            # 记录输出
            if stdout:
                self.logger.info(f"[{task['name']}] 输出: {stdout.strip()}")
            if stderr:
                self.logger.error(f"[{task['name']}] 错误: {stderr.strip()}")
            
            # 检查返回码
            success = process.returncode == 0
            if success:
                self.logger.info(f"✅ 任务 {task['name']} 执行成功")
            else:
                self.logger.error(f"❌ 任务 {task['name']} 执行失败，返回码: {process.returncode}")
            
            return success
            
        except subprocess.TimeoutExpired:
            self.logger.error(f"❌ 任务 {task['name']} 执行超时")
            process.kill()
            return False
        except Exception as e:
            self.logger.error(f"❌ 任务 {task['name']} 执行异常: {str(e)}")
            return False
    
    def _execute_task_with_retry(self, task: Dict[str, Any]):
        """带重试机制的任务执行"""
        task_name = task["name"]
        max_retry = self.config["settings"]["max_retry"]
        retry_delay = self.config["settings"]["retry_delay_minutes"]
        
        # 检查是否已完成
        if self._is_task_completed_today(task_name):
            self.logger.info(f"⏭️ 任务 {task_name} 今天已完成，跳过执行")
            return
        
        # 检查是否为交易日
        if not self._is_trading_day():
            self.logger.info(f"⏭️ 非交易日，跳过任务 {task_name}")
            return
        
        self.logger.info(f"🚀 开始执行任务: {task_name} - {task['description']}")
        
        for attempt in range(max_retry + 1):
            if attempt > 0:
                self.logger.info(f"🔄 重试执行任务 {task_name} (第{attempt}次重试)")
                time.sleep(retry_delay * 60)
            
            success = self._execute_script(task)
            
            if success:
                self._mark_task_completed(task_name, True)
                return
        
        # 所有重试都失败
        self.logger.error(f"❌ 任务 {task_name} 执行失败，已达到最大重试次数")
        self._mark_task_completed(task_name, False)

    def _schedule_startup_tasks(self):
        """调度开机启动任务"""
        startup_tasks = [task for task in self.config["tasks"]
                        if task.get("schedule_time", "").startswith("startup") and task.get("enabled", True)]

        if not startup_tasks:
            return

        def run_startup_tasks():
            # 解析延迟时间
            delay_minutes = 5  # 默认5分钟
            for task in startup_tasks:
                schedule_time = task.get("schedule_time", "")
                if "+" in schedule_time:
                    try:
                        delay_str = schedule_time.split("+")[1]
                        if "min" in delay_str:
                            delay_minutes = int(delay_str.replace("min", ""))
                    except:
                        pass
                break

            self.logger.info(f"⏰ 开机启动任务将在 {delay_minutes} 分钟后执行")
            time.sleep(delay_minutes * 60)

            if not self.startup_completed:
                for task in startup_tasks:
                    self._execute_task_with_retry(task)
                self.startup_completed = True

        # 在后台线程中执行启动任务
        startup_thread = threading.Thread(target=run_startup_tasks, daemon=True)
        startup_thread.start()

    def _schedule_daily_tasks(self):
        """调度每日定时任务"""
        daily_tasks = [task for task in self.config["tasks"]
                      if not task.get("schedule_time", "").startswith("startup") and task.get("enabled", True)]

        for task in daily_tasks:
            schedule_time = task.get("schedule_time")
            if schedule_time and ":" in schedule_time:
                schedule.every().day.at(schedule_time).do(self._execute_task_with_retry, task)
                self.logger.info(f"📅 已调度每日任务: {schedule_time} - {task['name']}")

    def _check_missed_tasks(self):
        """检查错过的任务并立即执行"""
        now = datetime.now()
        current_time = now.strftime("%H:%M")

        for task in self.config["tasks"]:
            if not task.get("enabled", True):
                continue

            schedule_time = task.get("schedule_time")
            if not schedule_time or ":" not in schedule_time:
                continue

            # 如果当前时间已过任务时间，且今天未完成，则立即执行
            if current_time > schedule_time and not self._is_task_completed_today(task["name"]):
                self.logger.info(f"⚡ 检测到错过的任务，立即执行: {task['name']}")
                threading.Thread(target=self._execute_task_with_retry, args=(task,), daemon=True).start()

    def start(self):
        """启动调度器"""
        self.logger.info("🚀 启动股票数据定时任务调度器...")

        # 调度开机启动任务
        self._schedule_startup_tasks()

        # 调度每日定时任务
        self._schedule_daily_tasks()

        # 检查错过的任务
        self._check_missed_tasks()

        # 显示调度信息
        self._show_schedule_info()

        self.running = True
        self.logger.info("✅ 调度器启动完成，等待任务执行...")

        # 主循环
        try:
            while self.running:
                schedule.run_pending()
                time.sleep(1)
        except KeyboardInterrupt:
            self.logger.info("收到中断信号，正在停止...")
        finally:
            self.stop()

    def stop(self):
        """停止调度器"""
        self.running = False
        schedule.clear()
        self.logger.info("🛑 调度器已停止")

    def _show_schedule_info(self):
        """显示调度信息"""
        self.logger.info("📋 当前调度任务:")

        for task in self.config["tasks"]:
            if task.get("enabled", True):
                schedule_time = task.get("schedule_time", "未设置")
                self.logger.info(f"  ⏰ {schedule_time}: {task['name']} - {task['description']}")

    def run_task_now(self, task_name: str):
        """立即执行指定任务"""
        for task in self.config["tasks"]:
            if task["name"] == task_name:
                self.logger.info(f"🚀 立即执行任务: {task_name}")
                self._execute_task_with_retry(task)
                return True

        self.logger.error(f"❌ 未找到任务: {task_name}")
        return False


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="股票数据定时任务调度器 - 简化版")
    parser.add_argument('--config', default='config.json', help='配置文件路径')
    parser.add_argument('--run-task', help='立即执行指定任务')

    args = parser.parse_args()

    try:
        # 创建调度器
        scheduler = SimpleStockScheduler(args.config)

        if args.run_task:
            # 立即执行指定任务
            success = scheduler.run_task_now(args.run_task)
            sys.exit(0 if success else 1)
        else:
            # 启动调度器
            scheduler.start()

    except FileNotFoundError as e:
        print(f"❌ 配置文件错误: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

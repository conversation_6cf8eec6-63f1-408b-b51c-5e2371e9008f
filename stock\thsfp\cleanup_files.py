#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理中间文件脚本
保留主要输入图片和最终处理文件，删除中间临时文件
"""

import shutil
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def cleanup_intermediate_files():
    """清理中间文件，只保留必要文件"""
    
    logger.info("🧹 开始清理中间文件...")
    
    # 要保留的文件模式
    keep_patterns = [
        "*.png",  # 原始图片
        "*_processed.xlsx",  # 最终处理文件
    ]
    
    # 要删除的目录和文件
    cleanup_targets = [
        "images/split_images",  # 切分图片目录
        "debug_images",  # 调试图片目录
        "output/*_temp.xlsx",  # 临时Excel文件
        "output/2025-*.xlsx",  # 中间合并文件（非processed）
    ]
    
    deleted_count = 0
    
    # 删除切分图片目录
    split_images_dir = Path("images/split_images")
    if split_images_dir.exists():
        try:
            shutil.rmtree(split_images_dir)
            logger.info(f"✅ 删除切分图片目录: {split_images_dir}")
            deleted_count += 1
        except Exception as e:
            logger.error(f"❌ 删除切分图片目录失败: {e}")
    
    # 删除调试图片目录
    debug_dir = Path("debug_images")
    if debug_dir.exists():
        try:
            shutil.rmtree(debug_dir)
            logger.info(f"✅ 删除调试图片目录: {debug_dir}")
            deleted_count += 1
        except Exception as e:
            logger.error(f"❌ 删除调试图片目录失败: {e}")
    
    # 删除临时Excel文件
    output_dir = Path("output")
    if output_dir.exists():
        # 删除temp文件
        temp_files = list(output_dir.glob("*_temp.xlsx"))
        for temp_file in temp_files:
            try:
                temp_file.unlink()
                logger.info(f"✅ 删除临时文件: {temp_file.name}")
                deleted_count += 1
            except Exception as e:
                logger.error(f"❌ 删除临时文件失败 {temp_file}: {e}")
        
        # 删除中间合并文件（保留processed文件）
        excel_files = list(output_dir.glob("2025-*.xlsx"))
        for excel_file in excel_files:
            if "_processed" not in excel_file.name:
                try:
                    excel_file.unlink()
                    logger.info(f"✅ 删除中间文件: {excel_file.name}")
                    deleted_count += 1
                except Exception as e:
                    logger.error(f"❌ 删除中间文件失败 {excel_file}: {e}")
    
    # 显示保留的文件
    logger.info("\n📁 保留的文件:")
    
    # 显示原始图片
    images_dir = Path("images")
    if images_dir.exists():
        png_files = list(images_dir.glob("*.png"))
        for png_file in png_files:
            logger.info(f"   📷 {png_file}")
    
    # 显示最终处理文件
    if output_dir.exists():
        processed_files = list(output_dir.glob("*_processed.xlsx"))
        for processed_file in processed_files:
            logger.info(f"   📊 {processed_file}")
    
    logger.info(f"\n🎉 清理完成，删除了 {deleted_count} 个中间文件/目录")
    
    return deleted_count

def main():
    """主函数"""
    try:
        deleted_count = cleanup_intermediate_files()
        if deleted_count > 0:
            logger.info("✅ 文件清理成功")
        else:
            logger.info("ℹ️ 没有需要清理的文件")
        return True
    except Exception as e:
        logger.error(f"❌ 文件清理失败: {e}")
        return False

if __name__ == "__main__":
    main()
